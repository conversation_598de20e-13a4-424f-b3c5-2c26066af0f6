# Import telegram modules with try/except to avoid IDE warnings
try:
    from telegram import Update
    from telegram.ext import CallbackContext
    from utils.constants import ParseMode
except ImportError:
    # Define placeholder classes for type hints
    class Update: pass
    class CallbackContext: pass
    class ParseMode:
        HTML = "HTML"
from utils.db import Database
from utils.keyboards import get_back_button
from utils.languages import get_message

def promo_command(update: Update, context: CallbackContext):
    """Handle /promo command"""
    user_id = update.effective_user.id
    message_text = update.message.text.strip()

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Check if a promo code was provided
    parts = message_text.split(maxsplit=1)

    if len(parts) == 1:
        # No promo code provided, show instructions
        update.message.reply_text(
            "To use a promo code, send it in the format: /promo YOUR_CODE\n"
            "Example: /promo TMCELL2024",
            reply_markup=get_back_button("main_menu", language)
        )
        return

    # Get the promo code
    promo_code = parts[1].strip().upper()

    # Try to use the promo code
    success, message = db.use_promo_code(user_id, promo_code)

    # Send the result
    update.message.reply_text(
        message,
        reply_markup=get_back_button("main_menu", language)
    )

def admin_create_promo(update: Update, context: CallbackContext):
    """Handle admin promo code creation with advanced options"""
    user_id = update.effective_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Check if message has the required format
    if not context.args or len(context.args) < 3:
        # Prepare help text based on language
        if language == 'ru':
            help_text = (
                "Расширенное создание промо-кода\n\n"
                "Базовое использование: /create_promo КОД БАЛЛЫ МАКС_ИСПОЛЬЗОВАНИЙ\n"
                "Пример: /create_promo SUMMER2024 10 100\n\n"
                "Расширенное использование: /create_promo КОД БАЛЛЫ МАКС_ИСПОЛЬЗОВАНИЙ СРОК_ДНЕЙ ТИП_ПРОМО VIP_ДНЕЙ\n"
                "Пример для промо с баллами: /create_promo SUMMER2024 10 100 30 points 0\n"
                "Пример для VIP промо: /create_promo VIP2024 0 50 30 vip 30\n"
                "Пример для комбинированного промо: /create_promo COMBO2024 5 50 30 combined 15\n\n"
                "ТИП_ПРОМО может быть: points, vip, или combined\n"
                "СРОК_ДНЕЙ: Количество дней до истечения срока действия промо-кода (0 для бессрочного)\n"
                "VIP_ДНЕЙ: Количество дней VIP-статуса (только для типов vip или combined)"
            )
        elif language == 'en':
            help_text = (
                "Advanced Promo Code Creation\n\n"
                "Basic usage: /create_promo CODE POINTS MAX_USES\n"
                "Example: /create_promo SUMMER2024 10 100\n\n"
                "Advanced usage: /create_promo CODE POINTS MAX_USES EXPIRY_DAYS PROMO_TYPE VIP_DAYS\n"
                "Example for points promo: /create_promo SUMMER2024 10 100 30 points 0\n"
                "Example for VIP promo: /create_promo VIP2024 0 50 30 vip 30\n"
                "Example for combined promo: /create_promo COMBO2024 5 50 30 combined 15\n\n"
                "PROMO_TYPE can be: points, vip, or combined\n"
                "EXPIRY_DAYS: Number of days until the promo code expires (0 for no expiry)\n"
                "VIP_DAYS: Number of days of VIP status (only for vip or combined types)"
            )
        else:  # Default to Turkmen
            help_text = (
                "Giňişleýin promo kod döretmek\n\n"
                "Esasy ulanyş: /create_promo KOD BALLAR MAX_ULANMALAR\n"
                "Mysal: /create_promo SUMMER2024 10 100\n\n"
                "Giňişleýin ulanyş: /create_promo KOD BALLAR MAX_ULANMALAR MÖHLET_GÜNLER PROMO_GÖRNÜŞI VIP_GÜNLER\n"
                "Ballar promo üçin mysal: /create_promo SUMMER2024 10 100 30 points 0\n"
                "VIP promo üçin mysal: /create_promo VIP2024 0 50 30 vip 30\n"
                "Kombinirlenýän promo üçin mysal: /create_promo COMBO2024 5 50 30 combined 15\n\n"
                "PROMO_GÖRNÜŞI şular bolup biler: points, vip, ýa-da combined\n"
                "MÖHLET_GÜNLER: Promo kodyň möhletiniň gutarmagyna çenli günleriň sany (0 - möhletsiz)\n"
                "VIP_GÜNLER: VIP statusynyň günleriniň sany (diňe vip ýa-da combined görnüşleri üçin)"
            )

        update.message.reply_text(
            help_text,
            reply_markup=get_back_button("admin_panel", language)
        )
        return

    # Get basic parameters
    code = context.args[0].upper()

    try:
        points = int(context.args[1])
        max_uses = int(context.args[2])

        # Get advanced parameters if provided
        expiry_days = None
        promo_type = 'points'
        vip_days = 0

        if len(context.args) >= 4:
            expiry_days_val = int(context.args[3])
            expiry_days = expiry_days_val if expiry_days_val > 0 else None

        if len(context.args) >= 5:
            promo_type = context.args[4].lower()
            if promo_type not in ['points', 'vip', 'combined']:
                promo_type = 'points'

        if len(context.args) >= 6:
            vip_days = int(context.args[5])

    except ValueError:
        # Error message based on language
        if language == 'ru':
            error_msg = "Ошибка: Все числовые параметры должны быть действительными числами."
        elif language == 'en':
            error_msg = "Error: All numeric parameters must be valid numbers."
        else:
            error_msg = "Ýalňyşlyk: Ähli san parametrleri dogry sanlar bolmaly."

        update.message.reply_text(
            error_msg,
            reply_markup=get_back_button("admin_panel", language)
        )
        return

    # Validate parameters based on promo type
    if promo_type == 'vip' and vip_days <= 0:
        if language == 'ru':
            error_msg = "Ошибка: VIP промо-коды должны иметь VIP_ДНЕЙ > 0."
        elif language == 'en':
            error_msg = "Error: VIP promo codes must have VIP_DAYS > 0."
        else:
            error_msg = "Ýalňyşlyk: VIP promo kodlary VIP_GÜNLER > 0 bolmaly."

        update.message.reply_text(
            error_msg,
            reply_markup=get_back_button("admin_panel", language)
        )
        return

    if promo_type == 'points' and points <= 0:
        if language == 'ru':
            error_msg = "Ошибка: Промо-коды с баллами должны иметь БАЛЛЫ > 0."
        elif language == 'en':
            error_msg = "Error: Points promo codes must have POINTS > 0."
        else:
            error_msg = "Ýalňyşlyk: Ballar promo kodlary BALLAR > 0 bolmaly."

        update.message.reply_text(
            error_msg,
            reply_markup=get_back_button("admin_panel", language)
        )
        return

    if promo_type == 'combined' and (points <= 0 or vip_days <= 0):
        if language == 'ru':
            error_msg = "Ошибка: Комбинированные промо-коды должны иметь и БАЛЛЫ > 0, и VIP_ДНЕЙ > 0."
        elif language == 'en':
            error_msg = "Error: Combined promo codes must have both POINTS > 0 and VIP_DAYS > 0."
        else:
            error_msg = "Ýalňyşlyk: Kombinirlenýän promo kodlary BALLAR > 0 we VIP_GÜNLER > 0 bolmaly."

        update.message.reply_text(
            error_msg,
            reply_markup=get_back_button("admin_panel", language)
        )
        return

    # Create the promo code with advanced options
    success, message = db.create_promo_code(code, points, max_uses, user_id, expiry_days, promo_type, vip_days)

    # Send the result
    update.message.reply_text(
        message,
        reply_markup=get_back_button("admin_panel", language)
    )

def admin_list_promos(update: Update, context: CallbackContext):
    """Handle admin promo code listing with advanced details"""
    user_id = update.effective_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get all promo codes
    promo_codes = db.get_promo_codes()

    if not promo_codes:
        # No promo codes message based on language
        if language == 'ru':
            no_codes_msg = "Промо-коды не найдены."
        elif language == 'en':
            no_codes_msg = "No promo codes found."
        else:
            no_codes_msg = "Promo kodlar tapylmady."

        update.message.reply_text(
            no_codes_msg,
            reply_markup=get_back_button("admin_panel", language)
        )
        return

    # Format the promo codes list based on language
    if language == 'ru':
        title = "📋 <b>Список промо-кодов:</b>\n\n"
        points_label = "баллов"
        vip_label = "дней VIP"
        points_type = "Промо с баллами"
        vip_type = "VIP промо"
        combined_type = "Комбинированное промо"
        uses_label = "Использований"
        created_label = "Создан"
        expires_label = "Истекает"
        expired_label = " (ИСТЕК)"
        never_label = "Никогда"
    elif language == 'en':
        title = "📋 <b>Promo Codes List:</b>\n\n"
        points_label = "points"
        vip_label = "VIP days"
        points_type = "Points Promo"
        vip_type = "VIP Promo"
        combined_type = "Combined Promo"
        uses_label = "Uses"
        created_label = "Created"
        expires_label = "Expires"
        expired_label = " (EXPIRED)"
        never_label = "Never"
    else:  # Default to Turkmen
        title = "📋 <b>Promo kodlaryň sanawy:</b>\n\n"
        points_label = "bal"
        vip_label = "VIP gün"
        points_type = "Ballar promo"
        vip_type = "VIP promo"
        combined_type = "Kombinirlenýän promo"
        uses_label = "Ulanmalar"
        created_label = "Döredilen"
        expires_label = "Möhleti gutarýar"
        expired_label = " (GUTARDY)"
        never_label = "Hiç haçan"

    message = title

    for i, promo in enumerate(promo_codes, 1):
        # Format based on promo type
        if promo['promo_type'] == 'points':
            message += f"{i}. <code>{promo['code']}</code> - {promo['points']} {points_label} ({points_type})\n"
        elif promo['promo_type'] == 'vip':
            message += f"{i}. <code>{promo['code']}</code> - {promo['vip_days']} {vip_label} ({vip_type})\n"
        else:  # combined
            message += f"{i}. <code>{promo['code']}</code> - {promo['points']} {points_label} + {promo['vip_days']} {vip_label} ({combined_type})\n"

        # Show usage stats
        message += f"   {uses_label}: {promo['current_uses']}/{promo['max_uses'] if promo['max_uses'] > 0 else '∞'}\n"

        # Show creation date
        message += f"   {created_label}: {promo['created_at']}\n"

        # Show expiry date if exists
        if promo['expiry_date']:
            status = expired_label if promo['is_expired'] else ""
            message += f"   {expires_label}: {promo['expiry_date']}{status}\n"
        else:
            message += f"   {expires_label}: {never_label}\n"

        message += "\n"

    # Send the list
    update.message.reply_text(
        message,
        parse_mode=ParseMode.HTML,
        reply_markup=get_back_button("admin_panel", language)
    )
