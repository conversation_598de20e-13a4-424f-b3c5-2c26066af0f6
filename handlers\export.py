from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext
from utils.db import Database
import tempfile
import os
import datetime

def export_history_command(update: Update, context: CallbackContext):
    """Handle /export command to export search history"""
    user_id = update.effective_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get user's search history
    history = db.get_search_history(user_id)

    if not history:
        # No history found
        if language == 'ru':
            update.message.reply_text("У вас нет истории поиска для экспорта.")
        elif language == 'en':
            update.message.reply_text("You have no search history to export.")
        else:
            update.message.reply_text("Eksport etmek üçin gözleg taryhy ýok.")
        return

    # Ask for export format
    keyboard = [
        [InlineKeyboardButton("📄 TXT", callback_data="export_txt")],
        [InlineKeyboardButton("📊 CSV", callback_data="export_csv")]
    ]

    if language == 'ru':
        update.message.reply_text(
            "Выберите формат для экспорта истории поиска:",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
    elif language == 'en':
        update.message.reply_text(
            "Choose a format to export your search history:",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
    else:
        update.message.reply_text(
            "Gözleg taryhy eksport etmek üçin format saýlaň:",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    # Store history in context for later use
    context.user_data['export_history'] = history

def export_history_callback(update: Update, context: CallbackContext):
    """Handle export format selection callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get history from context
    history = context.user_data.get('export_history', [])

    if not history:
        query.answer("No history to export")
        return

    export_format = query.data.split('_')[1]

    try:
        # Create a temporary file
        fd, path = tempfile.mkstemp(suffix=f'.{export_format}')

        if export_format == 'txt':
            # Export as TXT
            with os.fdopen(fd, 'w', encoding='utf-8') as file:
                # Write header
                if language == 'ru':
                    file.write("ИСТОРИЯ ПОИСКА\n\n")
                    file.write("Дата и время | Запрос | Тип поиска | Результаты\n")
                    file.write("-" * 60 + "\n")
                elif language == 'en':
                    file.write("SEARCH HISTORY\n\n")
                    file.write("Date and Time | Query | Search Type | Results\n")
                    file.write("-" * 60 + "\n")
                else:
                    file.write("GÖZLEG TARYHY\n\n")
                    file.write("Sene we wagt | Sorag | Gözleg görnüşi | Netijeler\n")
                    file.write("-" * 60 + "\n")

                # Write data
                for item in history:
                    file.write(f"{item['search_time']} | {item['query']} | {item['search_type']} | {item['results_count']}\n")

        elif export_format == 'csv':
            # Export as CSV (simple version without csv module)
            with os.fdopen(fd, 'w', encoding='utf-8') as file:
                # Write header
                if language == 'ru':
                    file.write("Дата и время,Запрос,Тип поиска,Результаты\n")
                elif language == 'en':
                    file.write("Date and Time,Query,Search Type,Results\n")
                else:
                    file.write("Sene we wagt,Sorag,Gözleg görnüşi,Netijeler\n")

                # Write data
                for item in history:
                    file.write(f"{item['search_time']},{item['query']},{item['search_type']},{item['results_count']}\n")

        # Send the file
        with open(path, 'rb') as file:
            current_time = datetime.datetime.now().strftime("%Y-%m-%d")
            filename = f"search_history_{current_time}.{export_format}"

            if language == 'ru':
                caption = "Ваша история поиска"
            elif language == 'en':
                caption = "Your search history"
            else:
                caption = "Siziň gözleg taryhy"

            query.bot.send_document(
                chat_id=user_id,
                document=file,
                filename=filename,
                caption=caption
            )

        # Inform user
        query.answer("Export completed!")

    except Exception as e:
        print(f"Error exporting history: {e}")
        query.answer("Error during export")
    finally:
        # Clean up
        try:
            os.unlink(path)
        except:
            pass
