from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from utils.constants import ParseMode
from telegram.ext import CallbackContext
from utils.db import Database
from config import ADMIN_IDS, OWNER_ID

def is_admin(user_id):
    """Check if a user is an admin"""
    return user_id in ADMIN_IDS

def admin_commands_callback(update: Update, context: CallbackContext):
    """Handle admin commands list callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Check if user is admin
    if not is_admin(user_id):
        query.answer("Bu funksiýa diňe administratorlar üçin elýeterlidir.")
        return

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Check if user is owner
    is_owner = user_id == OWNER_ID

    if is_owner:
        # Show owner commands
        return owner_commands_callback(update, context)

    # Create the admin commands list message
    commands_message = (
        "👑 <b>Admin Komandalaryň Sanawy</b>\n\n"

        "<b>� Umumy Komandalar:</b>\n"
        "�🔹 <code>/admin</code> - Admin paneli açýar\n"
        "🔹 <code>/admin_commands</code> - Bu komandalary görkezýär\n"
        "🔹 <code>/reload</code> - Bot baglanyşygyny täzeden başladýar\n"
        "🔹 <code>/restart</code> - Boty doly täzeden başladýar\n"
        "🔹 <code>/status</code> - Bot we ulgam statusyny görkezýär\n"
        "🔹 <code>/ping</code> - Botyň işleýşini barlaýar\n\n"

        "<b>👥 Ulanyjy Dolandyryş:</b>\n"
        "🔹 <code>/add_points [user_id] [points]</code> - Ulanyjynyň ballaryny artdyrýar\n"
        "   <i>Mysal: /add_points 123456789 5</i>\n"
        "🔹 <code>/remove_points [user_id] [points]</code> - Ulanyjynyň ballaryny azaldýar\n"
        "   <i>Mysal: /remove_points 123456789 3</i>\n"
        "🔹 <code>/set_vip [user_id] [1 or 0] [days]</code> - Ulanyjynyň VIP statusyny üýtgedýär\n"
        "   <i>Mysal: /set_vip 123456789 1 30 (30 gün VIP bermek üçin)</i>\n"
        "   <i>Mysal: /set_vip 123456789 0 (VIP statusyny aýyrmak üçin)</i>\n"
        "🔹 <code>/block_user [user_id]</code> - Ulanyjyny blokirlemek\n"
        "🔹 <code>/unblock_user [user_id]</code> - Ulanyjynyň blokirowkasyny aýyrmak\n\n"

        "<b>📊 Statistika:</b>\n"
        "🔹 <code>/view_stats [user_id]</code> - Ulanyjynyň statistikasyny görkezýär\n"
        "   <i>Mysal: /view_stats 123456789</i>\n"
        "🔹 <code>/stats</code> - Giňişleýin statistika panelini açýar\n"
        "🔹 <code>/search_stats</code> - Gözleg statistikasyny görkezýär\n"
        "🔹 <code>/export_stats</code> - Statistikany eksport etmek\n\n"

        "<b>� Bildiriş we Habarlaşma:</b>\n"
        "�🔹 <code>/broadcast [message]</code> - Ähli ulanyjylara habar iberýär\n"
        "   <i>Mysal: /broadcast Täze funksiýa goşuldy!</i>\n"
        "🔹 <code>/notify [user_id] [message]</code> - Belli bir ulanyjy üçin bildiriş ibermek\n"
        "🔹 <code>/create_notification BAŞLYK | HABAR | GÖRNÜŞ | MÖHLET_GÜNLER</code> - Täze bildiriş döredýär\n"
        "   <i>Mysal: /create_notification Täze funksiýa | Biz täze gözleg funksiýasyny goşduk! | feature | 7</i>\n\n"

        "<b>🎁 Promo Kodlar:</b>\n"
        "🔹 <code>/create_promo [CODE] [POINTS] [MAX_USES]</code> - Täze promo kod döredýär\n"
        "   <i>Mysal: /create_promo SUMMER2024 10 100</i>\n"
        "🔹 <code>/delete_promo [code]</code> - Promo kody pozmak\n"
        "🔹 <code>/list_promos</code> - Ähli promo kodlary görkezmek\n\n"

        "<b>🗄️ Maglumat Bazasy:</b>\n"
        "🔹 <code>/backup</code> - Maglumat bazasynyň ätiýaçlyk nusgasyny almak\n"
        "🔹 <code>/restore [file_id]</code> - Maglumat bazasyny dikeltmek\n"
        "🔹 <code>/clear_history</code> - Ähli gözleg taryhyny arassalamak\n\n"

        "<b>🔍 Gözleg:</b>\n"
        "🔹 <code>/search [query]</code> - Gözleg geçirmek\n"
        "🔹 <code>/bulk [queries]</code> - Köpçülikleýin gözleg geçirmek\n"
        "🔹 <code>/export</code> - Gözleg taryhyny eksport etmek"
    )

    # Create keyboard with back button
    keyboard = [
        [InlineKeyboardButton("⬅️ Yza", callback_data="admin_panel")]
    ]

    # Edit message with commands list
    query.edit_message_text(
        commands_message,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def owner_commands_callback(update: Update, context: CallbackContext):
    """Handle owner commands callback query"""
    query = update.callback_query
    user_id = query.from_user.id

    # Check if user is owner
    if user_id != OWNER_ID:
        query.answer("⚠️ Bu funksiýa diňe bot eýesi üçin elýeterlidir.")
        return

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Create a list of all owner commands with descriptions
    if language == 'ru':
        commands_text = "<b>👑 Команды владельца</b>\n\n"

        # User management commands
        commands_text += "<b>👥 Управление пользователями:</b>\n"
        commands_text += "/add_points [user_id] [points] - Добавить баллы пользователю\n"
        commands_text += "/remove_points [user_id] [points] - Удалить баллы у пользователя\n"
        commands_text += "/set_vip [user_id] [days] - Установить постоянный VIP статус пользователю\n"
        commands_text += "/set_vip_1 [user_id] - Установить VIP статус на 1 месяц (100 баллов)\n"
        commands_text += "/set_vip_3 [user_id] - Установить VIP статус на 3 месяца (300 баллов)\n"
        commands_text += "/set_vip_6 [user_id] - Установить VIP статус на 6 месяцев (1000 баллов)\n"
        commands_text += "/view_stats - Просмотр статистики пользователей\n"
        commands_text += "/new_admin [user_id] - Назначить нового администратора\n\n"

        # System commands
        commands_text += "<b>⚙️ Системные команды:</b>\n"
        commands_text += "/reload - Перезагрузить данные телефонов\n"
        commands_text += "/restart - Перезапустить бота\n"
        commands_text += "/status - Проверить статус бота\n"
        commands_text += "/broadcast [message] - Отправить сообщение всем пользователям\n"
        commands_text += "/create_notification [title] [message] - Создать уведомление\n\n"

        # Other commands
        commands_text += "<b>ℹ️ Другие команды:</b>\n"
        commands_text += "/admin - Открыть панель администратора\n"
        commands_text += "/admin_commands - Показать эту справку\n\n"

        commands_text += "👑 <b>Вы владелец бота! У вас есть доступ ко всем функциям.</b>"

        # Create keyboard
        keyboard = [
            [InlineKeyboardButton("🔙 Назад", callback_data="admin_panel")]
        ]
    elif language == 'en':
        commands_text = "<b>👑 Owner Commands</b>\n\n"

        # User management commands
        commands_text += "<b>👥 User Management:</b>\n"
        commands_text += "/add_points [user_id] [points] - Add points to a user\n"
        commands_text += "/remove_points [user_id] [points] - Remove points from a user\n"
        commands_text += "/set_vip [user_id] [days] - Set permanent VIP status for a user\n"
        commands_text += "/set_vip_1 [user_id] - Set VIP status for 1 month (100 points)\n"
        commands_text += "/set_vip_3 [user_id] - Set VIP status for 3 months (300 points)\n"
        commands_text += "/set_vip_6 [user_id] - Set VIP status for 6 months (1000 points)\n"
        commands_text += "/view_stats - View user statistics\n"
        commands_text += "/new_admin [user_id] - Assign a new administrator\n\n"

        # System commands
        commands_text += "<b>⚙️ System Commands:</b>\n"
        commands_text += "/reload - Reload phone data\n"
        commands_text += "/restart - Restart the bot\n"
        commands_text += "/status - Check bot status\n"
        commands_text += "/broadcast [message] - Send a message to all users\n"
        commands_text += "/create_notification [title] [message] - Create a notification\n\n"

        # Other commands
        commands_text += "<b>ℹ️ Other Commands:</b>\n"
        commands_text += "/admin - Open admin panel\n"
        commands_text += "/admin_commands - Show this help\n\n"

        commands_text += "👑 <b>You are the bot owner! You have access to all features.</b>"

        # Create keyboard
        keyboard = [
            [InlineKeyboardButton("🔙 Back", callback_data="admin_panel")]
        ]
    else:
        commands_text = "<b>👑 Eýe Komandalar</b>\n\n"

        # User management commands
        commands_text += "<b>👥 Ulanyjy Dolandyryş:</b>\n"
        commands_text += "/add_points [user_id] [points] - Ulanyja bal goşmak\n"
        commands_text += "/remove_points [user_id] [points] - Ulanyja bal aýyrmak\n"
        commands_text += "/set_vip [user_id] [days] - Ulanyja hemişelik VIP statusy bermek\n"
        commands_text += "/set_vip_1 [user_id] - Ulanyja 1 aýlyk VIP statusy bermek (100 bal)\n"
        commands_text += "/set_vip_3 [user_id] - Ulanyja 3 aýlyk VIP statusy bermek (300 bal)\n"
        commands_text += "/set_vip_6 [user_id] - Ulanyja 6 aýlyk VIP statusy bermek (1000 bal)\n"
        commands_text += "/view_stats - Ulanyjy statistikasyny görmek\n"
        commands_text += "/new_admin [user_id] - Täze administrator bellemek\n\n"

        # System commands
        commands_text += "<b>⚙️ Ulgam Komandalary:</b>\n"
        commands_text += "/reload - Telefon maglumatlaryny täzelemek\n"
        commands_text += "/restart - Boty täzeden başlatmak\n"
        commands_text += "/status - Bot ýagdaýyny barlamak\n"
        commands_text += "/broadcast [message] - Ähli ullanyjylara habar ibermek\n"
        commands_text += "/create_notification [title] [message] - Bildiriş döretmek\n\n"

        # Other commands
        commands_text += "<b>ℹ️ Beýleki Komandalar:</b>\n"
        commands_text += "/admin - Admin paneli açmak\n"
        commands_text += "/admin_commands - Bu kömegi görkezmek\n\n"

        commands_text += "👑 <b>Siz botyň eýesisiniz! Size ähli funksiýalar elýeterlidir.</b>"

        # Create keyboard
        keyboard = [
            [InlineKeyboardButton("🔙 Yza", callback_data="admin_panel")]
        ]

    # Send the commands list
    query.edit_message_text(
        commands_text,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def admin_commands_command(update: Update, context: CallbackContext):
    """Handle /admin_commands command"""
    user_id = update.effective_user.id

    # Check if user is admin
    if not is_admin(user_id):
        update.message.reply_text("⛔ Bu komanda diňe administratorlar üçin elýeterlidir.")
        return

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Check if user is owner
    is_owner = user_id == OWNER_ID

    if is_owner:
        # Create the owner commands list message
        if language == 'ru':
            commands_message = (
                "👑 <b>Команды владельца</b>\n\n"

                "<b>👥 Управление пользователями:</b>\n"
                "🔹 <code>/add_points [user_id] [points]</code> - Добавить баллы пользователю\n"
                "   <i>Пример: /add_points 123456789 5</i>\n"
                "🔹 <code>/remove_points [user_id] [points]</code> - Удалить баллы у пользователя\n"
                "   <i>Пример: /remove_points 123456789 3</i>\n"
                "🔹 <code>/set_vip [user_id] [1 or 0] [days]</code> - Установить постоянный VIP статус пользователю\n"
                "   <i>Пример: /set_vip 123456789 1 (постоянный VIP)</i>\n"
                "🔹 <code>/set_vip_1 [user_id]</code> - Установить VIP статус на 1 месяц (100 баллов)\n"
                "   <i>Пример: /set_vip_1 123456789</i>\n"
                "🔹 <code>/set_vip_3 [user_id]</code> - Установить VIP статус на 3 месяца (300 баллов)\n"
                "   <i>Пример: /set_vip_3 123456789</i>\n"
                "🔹 <code>/set_vip_6 [user_id]</code> - Установить VIP статус на 6 месяцев (1000 баллов)\n"
                "   <i>Пример: /set_vip_6 123456789</i>\n"
                "🔹 <code>/new_admin [user_id]</code> - Назначить нового администратора\n\n"

                "<b>⚙️ Системные команды:</b>\n"
                "🔹 <code>/reload</code> - Перезагрузить данные телефонов\n"
                "🔹 <code>/restart</code> - Перезапустить бота\n"
                "🔹 <code>/status</code> - Проверить статус бота\n"
                "🔹 <code>/broadcast [message]</code> - Отправить сообщение всем пользователям\n\n"

                "👑 <b>Вы владелец бота! У вас есть доступ ко всем функциям.</b>"
            )
        elif language == 'en':
            commands_message = (
                "👑 <b>Owner Commands</b>\n\n"

                "<b>👥 User Management:</b>\n"
                "🔹 <code>/add_points [user_id] [points]</code> - Add points to a user\n"
                "   <i>Example: /add_points 123456789 5</i>\n"
                "🔹 <code>/remove_points [user_id] [points]</code> - Remove points from a user\n"
                "   <i>Example: /remove_points 123456789 3</i>\n"
                "🔹 <code>/set_vip [user_id] [1 or 0] [days]</code> - Set permanent VIP status for a user\n"
                "   <i>Example: /set_vip 123456789 1 (permanent VIP)</i>\n"
                "🔹 <code>/set_vip_1 [user_id]</code> - Set VIP status for 1 month (100 points)\n"
                "   <i>Example: /set_vip_1 123456789</i>\n"
                "🔹 <code>/set_vip_3 [user_id]</code> - Set VIP status for 3 months (300 points)\n"
                "   <i>Example: /set_vip_3 123456789</i>\n"
                "🔹 <code>/set_vip_6 [user_id]</code> - Set VIP status for 6 months (1000 points)\n"
                "   <i>Example: /set_vip_6 123456789</i>\n"
                "🔹 <code>/new_admin [user_id]</code> - Assign a new administrator\n\n"

                "<b>⚙️ System Commands:</b>\n"
                "🔹 <code>/reload</code> - Reload phone data\n"
                "🔹 <code>/restart</code> - Restart the bot\n"
                "🔹 <code>/status</code> - Check bot status\n"
                "🔹 <code>/broadcast [message]</code> - Send a message to all users\n\n"

                "👑 <b>You are the bot owner! You have access to all features.</b>"
            )
        else:
            commands_message = (
                "👑 <b>Eýe Komandalar</b>\n\n"

                "<b>👥 Ulanyjy Dolandyryş:</b>\n"
                "🔹 <code>/add_points [user_id] [points]</code> - Ulanyja bal goşmak\n"
                "   <i>Mysal: /add_points 123456789 5</i>\n"
                "🔹 <code>/remove_points [user_id] [points]</code> - Ulanyja bal aýyrmak\n"
                "   <i>Mysal: /remove_points 123456789 3</i>\n"
                "🔹 <code>/set_vip [user_id] [1 or 0] [days]</code> - Ulanyja hemişelik VIP statusy bermek\n"
                "   <i>Mysal: /set_vip 123456789 1 (hemişelik VIP)</i>\n"
                "🔹 <code>/set_vip_1 [user_id]</code> - Ulanyja 1 aýlyk VIP statusy bermek (100 bal)\n"
                "   <i>Mysal: /set_vip_1 123456789</i>\n"
                "🔹 <code>/set_vip_3 [user_id]</code> - Ulanyja 3 aýlyk VIP statusy bermek (300 bal)\n"
                "   <i>Mysal: /set_vip_3 123456789</i>\n"
                "🔹 <code>/set_vip_6 [user_id]</code> - Ulanyja 6 aýlyk VIP statusy bermek (1000 bal)\n"
                "   <i>Mysal: /set_vip_6 123456789</i>\n"
                "🔹 <code>/new_admin [user_id]</code> - Täze administrator bellemek\n\n"

                "<b>⚙️ Ulgam Komandalary:</b>\n"
                "🔹 <code>/reload</code> - Telefon maglumatlaryny täzelemek\n"
                "🔹 <code>/restart</code> - Boty täzeden başlatmak\n"
                "🔹 <code>/status</code> - Bot ýagdaýyny barlamak\n"
                "🔹 <code>/broadcast [message]</code> - Ähli ullanyjylara habar ibermek\n\n"

                "👑 <b>Siz botyň eýesisiniz! Size ähli funksiýalar elýeterlidir.</b>"
            )

        # Send the commands list
        update.message.reply_text(
            commands_message,
            parse_mode=ParseMode.HTML
        )
        return

    # Create the admin commands list message
    commands_message = (
        "👑 <b>Admin Komandalaryň Sanawy</b>\n\n"

        "<b>📋 Umumy Komandalar:</b>\n"
        "🔹 <code>/admin</code> - Admin paneli açýar\n"
        "🔹 <code>/admin_commands</code> - Bu komandalary görkezýär\n"
        "🔹 <code>/reload</code> - Bot baglanyşygyny täzeden başladýar\n"
        "🔹 <code>/restart</code> - Boty doly täzeden başladýar\n"
        "🔹 <code>/status</code> - Bot we ulgam statusyny görkezýär\n"
        "🔹 <code>/ping</code> - Botyň işleýşini barlaýar\n\n"

        "<b>👥 Ulanyjy Dolandyryş:</b>\n"
        "🔹 <code>/add_points [user_id] [points]</code> - Ulanyjynyň ballaryny artdyrýar\n"
        "   <i>Mysal: /add_points 123456789 5</i>\n"
        "🔹 <code>/remove_points [user_id] [points]</code> - Ulanyjynyň ballaryny azaldýar\n"
        "   <i>Mysal: /remove_points 123456789 3</i>\n"
        "🔹 <code>/set_vip [user_id] [1 or 0] [days]</code> - Ulanyjynyň hemişelik VIP statusyny üýtgedýär\n"
        "   <i>Mysal: /set_vip 123456789 1 (hemişelik VIP bermek üçin)</i>\n"
        "   <i>Mysal: /set_vip 123456789 0 (VIP statusyny aýyrmak üçin)</i>\n"
        "🔹 <code>/set_vip_1 [user_id]</code> - Ulanyja 1 aýlyk VIP statusy bermek (100 bal)\n"
        "   <i>Mysal: /set_vip_1 123456789</i>\n"
        "🔹 <code>/set_vip_3 [user_id]</code> - Ulanyja 3 aýlyk VIP statusy bermek (300 bal)\n"
        "   <i>Mysal: /set_vip_3 123456789</i>\n"
        "🔹 <code>/set_vip_6 [user_id]</code> - Ulanyja 6 aýlyk VIP statusy bermek (1000 bal)\n"
        "   <i>Mysal: /set_vip_6 123456789</i>\n"
        "🔹 <code>/block_user [user_id]</code> - Ulanyjyny blokirlemek\n"
        "🔹 <code>/unblock_user [user_id]</code> - Ulanyjynyň blokirowkasyny aýyrmak\n\n"

        "<b>📊 Statistika:</b>\n"
        "🔹 <code>/view_stats [user_id]</code> - Ulanyjynyň statistikasyny görkezýär\n"
        "   <i>Mysal: /view_stats 123456789</i>\n"
        "🔹 <code>/stats</code> - Giňişleýin statistika panelini açýar\n"
        "🔹 <code>/search_stats</code> - Gözleg statistikasyny görkezýär\n"
        "🔹 <code>/export_stats</code> - Statistikany eksport etmek\n\n"

        "<b>📢 Bildiriş we Habarlaşma:</b>\n"
        "🔹 <code>/broadcast [message]</code> - Ähli ulanyjylara habar iberýär\n"
        "   <i>Mysal: /broadcast Täze funksiýa goşuldy!</i>\n"
        "🔹 <code>/notify [user_id] [message]</code> - Belli bir ulanyjy üçin bildiriş ibermek\n"
        "🔹 <code>/create_notification BAŞLYK | HABAR | GÖRNÜŞ | MÖHLET_GÜNLER</code> - Täze bildiriş döredýär\n"
        "   <i>Mysal: /create_notification Täze funksiýa | Biz täze gözleg funksiýasyny goşduk! | feature | 7</i>\n\n"

        "<b>🎁 Promo Kodlar:</b>\n"
        "🔹 <code>/create_promo [CODE] [POINTS] [MAX_USES]</code> - Täze promo kod döredýär\n"
        "   <i>Mysal: /create_promo SUMMER2024 10 100</i>\n"
        "🔹 <code>/delete_promo [code]</code> - Promo kody pozmak\n"
        "🔹 <code>/list_promos</code> - Ähli promo kodlary görkezmek\n\n"

        "<b>🗄️ Maglumat Bazasy:</b>\n"
        "🔹 <code>/backup</code> - Maglumat bazasynyň ätiýaçlyk nusgasyny almak\n"
        "🔹 <code>/restore [file_id]</code> - Maglumat bazasyny dikeltmek\n"
        "🔹 <code>/clear_history</code> - Ähli gözleg taryhyny arassalamak\n\n"

        "<b>🔍 Gözleg:</b>\n"
        "🔹 <code>/search [query]</code> - Gözleg geçirmek\n"
        "🔹 <code>/bulk [queries]</code> - Köpçülikleýin gözleg geçirmek\n"
        "🔹 <code>/export</code> - Gözleg taryhyny eksport etmek"
    )

    # Create keyboard with back button
    keyboard = [
        [InlineKeyboardButton("⬅️ Yza", callback_data="admin_panel")]
    ]

    # Send message with commands list
    update.message.reply_text(
        commands_message,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )
