from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext
from utils.db import Database
import qrcode
import io
import os
import datetime

def referral_command(update: Update, context: CallbackContext):
    """Handle the /referral command"""
    user_id = update.effective_user.id
    
    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)
    
    # Get user's referral count
    referral_count = db.get_referral_count(user_id)
    
    # Get user's referral link
    from config import BOT_USERNAME
    referral_link = f"https://t.me/{BOT_USERNAME.replace('@', '')}?start={user_id}"
    
    # Create keyboard with referral options
    keyboard = [
        [InlineKeyboardButton("QR kod döret", callback_data="referral_qr")],
        [InlineKeyboardButton("Referral statistikasy", callback_data="referral_stats")],
        [InlineKeyboardButton("Yza", callback_data="main_menu")]
    ]
    
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    # Create message based on language
    if language == 'ru':
        message = f"👥 <b>Реферальная программа</b>\n\n"
        message += f"Приглашайте друзей и получайте бонусы!\n\n"
        message += f"• За каждого приглашенного пользователя вы получаете 1 балл поиска.\n"
        message += f"• Приглашенные вами: {referral_count}\n\n"
        message += f"Ваша реферальная ссылка:\n<code>{referral_link}</code>\n\n"
        message += f"Поделитесь этой ссылкой с друзьями или создайте QR-код."
    elif language == 'en':
        message = f"👥 <b>Referral Program</b>\n\n"
        message += f"Invite friends and get bonuses!\n\n"
        message += f"• For each invited user, you get 1 search point.\n"
        message += f"• Users invited by you: {referral_count}\n\n"
        message += f"Your referral link:\n<code>{referral_link}</code>\n\n"
        message += f"Share this link with friends or create a QR code."
    else:
        message = f"👥 <b>Referral programmasy</b>\n\n"
        message += f"Dostlaryňyzy çagyryň we bonuslar alyň!\n\n"
        message += f"• Her çagyran ulanyjyňyz üçin 1 gözleg baly alarsyňyz.\n"
        message += f"• Siziň çagyran ulanyjylaryňyz: {referral_count}\n\n"
        message += f"Siziň referral baglanyşygyňyz:\n<code>{referral_link}</code>\n\n"
        message += f"Bu baglanyşygy dostlaryňyz bilen paýlaşyň ýa-da QR kod dörediň."
    
    # Send message
    update.message.reply_text(message, reply_markup=reply_markup, parse_mode='HTML')

def referral_callback(update: Update, context: CallbackContext):
    """Handle referral callbacks"""
    query = update.callback_query
    user_id = query.from_user.id
    callback_data = query.data
    
    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)
    
    # Handle different referral options
    if callback_data == "referral_qr":
        generate_referral_qr(query, db, language)
    elif callback_data == "referral_stats":
        show_referral_stats(query, db, language)
    elif callback_data == "referral_back":
        # Return to referral menu
        referral_menu(query, db, language)

def referral_menu(query, db, language):
    """Show referral menu"""
    user_id = query.from_user.id
    
    # Get user's referral count
    referral_count = db.get_referral_count(user_id)
    
    # Get user's referral link
    from config import BOT_USERNAME
    referral_link = f"https://t.me/{BOT_USERNAME.replace('@', '')}?start={user_id}"
    
    # Create keyboard with referral options
    keyboard = [
        [InlineKeyboardButton("QR kod döret", callback_data="referral_qr")],
        [InlineKeyboardButton("Referral statistikasy", callback_data="referral_stats")],
        [InlineKeyboardButton("Yza", callback_data="main_menu")]
    ]
    
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    # Create message based on language
    if language == 'ru':
        message = f"👥 <b>Реферальная программа</b>\n\n"
        message += f"Приглашайте друзей и получайте бонусы!\n\n"
        message += f"• За каждого приглашенного пользователя вы получаете 1 балл поиска.\n"
        message += f"• Приглашенные вами: {referral_count}\n\n"
        message += f"Ваша реферальная ссылка:\n<code>{referral_link}</code>\n\n"
        message += f"Поделитесь этой ссылкой с друзьями или создайте QR-код."
    elif language == 'en':
        message = f"👥 <b>Referral Program</b>\n\n"
        message += f"Invite friends and get bonuses!\n\n"
        message += f"• For each invited user, you get 1 search point.\n"
        message += f"• Users invited by you: {referral_count}\n\n"
        message += f"Your referral link:\n<code>{referral_link}</code>\n\n"
        message += f"Share this link with friends or create a QR code."
    else:
        message = f"👥 <b>Referral programmasy</b>\n\n"
        message += f"Dostlaryňyzy çagyryň we bonuslar alyň!\n\n"
        message += f"• Her çagyran ulanyjyňyz üçin 1 gözleg baly alarsyňyz.\n"
        message += f"• Siziň çagyran ulanyjylaryňyz: {referral_count}\n\n"
        message += f"Siziň referral baglanyşygyňyz:\n<code>{referral_link}</code>\n\n"
        message += f"Bu baglanyşygy dostlaryňyz bilen paýlaşyň ýa-da QR kod dörediň."
    
    # Edit message
    query.edit_message_text(message, reply_markup=reply_markup, parse_mode='HTML')

def generate_referral_qr(query, db, language):
    """Generate QR code for referral link"""
    user_id = query.from_user.id
    
    # Get user's referral link
    from config import BOT_USERNAME
    referral_link = f"https://t.me/{BOT_USERNAME.replace('@', '')}?start={user_id}"
    
    # Create directory for QR codes if it doesn't exist
    if not os.path.exists('qr_codes'):
        os.makedirs('qr_codes')
    
    # Generate filename with timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"qr_codes/referral_{user_id}_{timestamp}.png"
    
    try:
        # Generate QR code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(referral_link)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        img.save(filename)
        
        # Send QR code
        with open(filename, 'rb') as photo:
            # Create caption based on language
            if language == 'ru':
                caption = f"👥 <b>Ваша реферальная ссылка - QR код</b>\n\n"
                caption += f"Отсканируйте этот QR-код, чтобы присоединиться к боту.\n\n"
                caption += f"За каждого приглашенного пользователя вы получаете 1 балл поиска."
            elif language == 'en':
                caption = f"👥 <b>Your Referral Link - QR Code</b>\n\n"
                caption += f"Scan this QR code to join the bot.\n\n"
                caption += f"For each invited user, you get 1 search point."
            else:
                caption = f"👥 <b>Siziň referral baglanyşygyňyz - QR kod</b>\n\n"
                caption += f"Bota goşulmak üçin bu QR kody skanirläň.\n\n"
                caption += f"Her çagyran ulanyjyňyz üçin 1 gözleg baly alarsyňyz."
            
            query.message.reply_photo(
                photo=photo,
                caption=caption,
                parse_mode='HTML'
            )
        
        # Answer the callback query
        if language == 'ru':
            query.answer("QR-код успешно создан!")
        elif language == 'en':
            query.answer("QR code successfully created!")
        else:
            query.answer("QR kod üstünlikli döredildi!")
        
        # Create back button
        keyboard = [[InlineKeyboardButton("Yza", callback_data="referral_back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        # Update message
        if language == 'ru':
            message = f"👥 <b>Реферальная программа</b>\n\n"
            message += f"QR-код успешно создан и отправлен выше.\n\n"
            message += f"Поделитесь этим QR-кодом с друзьями, чтобы они могли присоединиться к боту."
        elif language == 'en':
            message = f"👥 <b>Referral Program</b>\n\n"
            message += f"QR code successfully created and sent above.\n\n"
            message += f"Share this QR code with friends so they can join the bot."
        else:
            message = f"👥 <b>Referral programmasy</b>\n\n"
            message += f"QR kod üstünlikli döredildi we ýokarda iberildi.\n\n"
            message += f"Dostlaryňyzyň bota goşulmagy üçin bu QR kody paýlaşyň."
        
        query.edit_message_text(message, reply_markup=reply_markup, parse_mode='HTML')
        
    except Exception as e:
        print(f"Error creating QR code: {e}")
        if language == 'ru':
            query.answer("Ошибка при создании QR-кода.")
        elif language == 'en':
            query.answer("Error creating QR code.")
        else:
            query.answer("QR kod döretmekde ýalňyşlyk.")
    
    # Clean up the file
    try:
        if os.path.exists(filename):
            os.remove(filename)
    except:
        pass

def show_referral_stats(query, db, language):
    """Show referral statistics"""
    user_id = query.from_user.id
    
    # Get user's referrals
    db.cur.execute("""
        SELECT u.user_id, u.username, u.full_name, r.join_date
        FROM referrals r
        JOIN users u ON r.referred_id = u.user_id
        WHERE r.referrer_id = ?
        ORDER BY r.join_date DESC
        LIMIT 10
    """, (user_id,))
    
    referrals = db.cur.fetchall()
    
    # Create message based on language
    if language == 'ru':
        message = f"👥 <b>Статистика рефералов</b>\n\n"
        message += f"Всего приглашенных пользователей: {len(referrals)}\n\n"
        
        if referrals:
            message += f"<b>Последние приглашенные пользователи:</b>\n"
            for i, referral in enumerate(referrals, 1):
                username = referral[1] if referral[1] else "Нет имени пользователя"
                full_name = referral[2] if referral[2] else "Неизвестно"
                join_date = referral[3]
                
                message += f"{i}. {full_name} (@{username})\n"
                message += f"   Дата: {join_date}\n"
        else:
            message += f"У вас пока нет приглашенных пользователей."
    elif language == 'en':
        message = f"👥 <b>Referral Statistics</b>\n\n"
        message += f"Total invited users: {len(referrals)}\n\n"
        
        if referrals:
            message += f"<b>Recently invited users:</b>\n"
            for i, referral in enumerate(referrals, 1):
                username = referral[1] if referral[1] else "No username"
                full_name = referral[2] if referral[2] else "Unknown"
                join_date = referral[3]
                
                message += f"{i}. {full_name} (@{username})\n"
                message += f"   Date: {join_date}\n"
        else:
            message += f"You don't have any invited users yet."
    else:
        message = f"👥 <b>Referral statistikasy</b>\n\n"
        message += f"Jemi çagyrylan ulanyjylar: {len(referrals)}\n\n"
        
        if referrals:
            message += f"<b>Soňky çagyrylan ulanyjylar:</b>\n"
            for i, referral in enumerate(referrals, 1):
                username = referral[1] if referral[1] else "Ulanyjy ady ýok"
                full_name = referral[2] if referral[2] else "Näbelli"
                join_date = referral[3]
                
                message += f"{i}. {full_name} (@{username})\n"
                message += f"   Sene: {join_date}\n"
        else:
            message += f"Heniz çagyran ulanyjyňyz ýok."
    
    # Create back button
    keyboard = [[InlineKeyboardButton("Yza", callback_data="referral_back")]]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    # Edit message
    query.edit_message_text(message, reply_markup=reply_markup, parse_mode='HTML')