from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ParseMode
from telegram.ext import CallbackContext
from utils.db import Database
from utils.languages import get_message

def faq_command(update: Update, context: CallbackContext):
    """Handle /faq command"""
    user_id = update.effective_user.id
    
    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)
    
    # Get FAQ content
    faq_title = get_message('faq_title', language)
    faq_content = get_message('faq_content', language)
    
    # Create keyboard
    keyboard = [
        [InlineKeyboardButton(get_message('support_button', language), callback_data="support")],
        [InlineKeyboardButton(get_message('back', language), callback_data="main_menu")]
    ]
    
    # Send FAQ message
    update.message.reply_text(
        f"<b>{faq_title}</b>\n\n{faq_content}",
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )
