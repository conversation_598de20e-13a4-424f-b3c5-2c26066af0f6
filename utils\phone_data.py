def get_user_data_by_phone(phone):
    """Uly JSON faýlyndan diňe gerek wagty maglumat okamak üçin funk<PERSON>."""
    import json
    with open('all_phone_data.json', 'r', encoding='utf-8') as f:
        buffer = ''
        inside_array = False
        for line in f:
            line = line.strip()
            if not inside_array:
                if line.startswith('['):
                    inside_array = True
                continue
            if line.endswith(']'):
                break
            if line.endswith(','):
                line = line[:-1]
            if line:
                try:
                    record = json.loads(line)
                    if record.get('phone') == phone:
                        return record
                except Exception:
                    continue
    return None
