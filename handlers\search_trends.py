"""
Search trends module for analyzing popular search patterns
"""
import logging
from utils.db import Database
import datetime
import json
import os
from collections import Counter
import matplotlib.pyplot as plt
import numpy as np

# Set up logging
logging.basicConfig(format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO)
logger = logging.getLogger(__name__)

def get_search_trends(days=30, limit=10):
    """
    Get trending searches over a period of time
    
    Args:
        days: Number of days to look back
        limit: Maximum number of trends to return
        
    Returns:
        List of trending search terms with counts
    """
    try:
        db = Database()
        
        # Calculate date range
        end_date = datetime.datetime.now()
        start_date = end_date - datetime.timedelta(days=days)
        
        # Format dates for SQL query
        start_date_str = start_date.strftime('%Y-%m-%d %H:%M:%S')
        end_date_str = end_date.strftime('%Y-%m-%d %H:%M:%S')
        
        # Get search queries
        db.cur.execute("""
            SELECT query
            FROM searches
            WHERE search_date BETWEEN ? AND ?
        """, (start_date_str, end_date_str))
        
        queries = [row[0] for row in db.cur.fetchall()]
        
        # Count occurrences
        query_counts = Counter(queries)
        
        # Get most common
        trends = query_counts.most_common(limit)
        
        return [{'query': query, 'count': count} for query, count in trends]
    
    except Exception as e:
        logger.error(f"Error getting search trends: {e}")
        return []

def generate_trends_graph(days=30, filename=None):
    """
    Generate a graph of search trends
    
    Args:
        days: Number of days to look back
        filename: Optional filename to save the graph
        
    Returns:
        Path to saved graph or None if error
    """
    try:
        # Get trends
        trends = get_search_trends(days, limit=10)
        
        if not trends:
            return None
        
        # Extract data
        queries = [item['query'] for item in trends]
        counts = [item['count'] for item in trends]
        
        # Create directory if it doesn't exist
        os.makedirs('stats/graphs', exist_ok=True)
        
        # Generate filename if not provided
        if not filename:
            current_date = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"stats/graphs/search_trends_{current_date}.png"
        
        # Create graph
        plt.figure(figsize=(10, 6))
        plt.bar(range(len(queries)), counts, color='skyblue')
        plt.xlabel('Gözleg')
        plt.ylabel('Sany')
        plt.title(f'Iň köp gözlenenler (soňky {days} gün)')
        plt.xticks(range(len(queries)), queries, rotation=45, ha='right')
        plt.tight_layout()
        
        # Save graph
        plt.savefig(filename)
        plt.close()
        
        return filename
    
    except Exception as e:
        logger.error(f"Error generating trends graph: {e}")
        return None

def get_search_patterns_by_time():
    """
    Get search patterns by time of day
    
    Returns:
        Dictionary with hourly search counts
    """
    try:
        db = Database()
        
        # Get searches from the last 30 days
        end_date = datetime.datetime.now()
        start_date = end_date - datetime.timedelta(days=30)
        
        # Format dates for SQL query
        start_date_str = start_date.strftime('%Y-%m-%d %H:%M:%S')
        end_date_str = end_date.strftime('%Y-%m-%d %H:%M:%S')
        
        # Get search times
        db.cur.execute("""
            SELECT search_date
            FROM searches
            WHERE search_date BETWEEN ? AND ?
        """, (start_date_str, end_date_str))
        
        search_dates = db.cur.fetchall()
        
        # Count by hour
        hour_counts = Counter()
        
        for (search_date,) in search_dates:
            try:
                date_obj = datetime.datetime.strptime(search_date, '%Y-%m-%d %H:%M:%S')
                hour = date_obj.hour
                hour_counts[hour] += 1
            except:
                pass
        
        # Convert to sorted dictionary
        result = {}
        for hour in range(24):
            result[hour] = hour_counts.get(hour, 0)
        
        return result
    
    except Exception as e:
        logger.error(f"Error getting search patterns by time: {e}")
        return {}

def generate_hourly_pattern_graph(filename=None):
    """
    Generate a graph of hourly search patterns
    
    Args:
        filename: Optional filename to save the graph
        
    Returns:
        Path to saved graph or None if error
    """
    try:
        # Get hourly patterns
        patterns = get_search_patterns_by_time()
        
        if not patterns:
            return None
        
        # Extract data
        hours = list(range(24))
        counts = [patterns.get(hour, 0) for hour in hours]
        
        # Create directory if it doesn't exist
        os.makedirs('stats/graphs', exist_ok=True)
        
        # Generate filename if not provided
        if not filename:
            current_date = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"stats/graphs/hourly_pattern_{current_date}.png"
        
        # Create graph
        plt.figure(figsize=(12, 6))
        plt.plot(hours, counts, marker='o', linestyle='-', color='blue')
        plt.xlabel('Sagat')
        plt.ylabel('Gözleg sany')
        plt.title('Sagatlar boýunça gözleg statistikasy')
        plt.xticks(hours)
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        
        # Save graph
        plt.savefig(filename)
        plt.close()
        
        return filename
    
    except Exception as e:
        logger.error(f"Error generating hourly pattern graph: {e}")
        return None