from telegram import Update
from telegram.ext import CallbackContext
from utils.db import Database
from config import ADMIN_IDS, OWNER_ID

def handle_set_vip_perm_command(update: Update, context: CallbackContext):
    """Handle permanent VIP setting command - only available to owner"""
    user_id = update.effective_user.id
    message_text = update.message.text

    # Check if user is owner
    if user_id != OWNER_ID:
        update.message.reply_text("⚠️ Bu komanda diňe bot eýesi üçin elýeterlidir.")
        return

    # Get database connection
    db = Database()

    # Print debug information
    print(f"Processing command: {message_text} from user {user_id}")

    try:
        # Parse command
        parts = message_text.split()
        if len(parts) != 2:
            update.message.reply_text("Invalid format. Use: /set_vip_perm [user_id]")
            return

        target_user_id = int(parts[1])

        # Check if user exists
        if not db.user_exists(target_user_id):
            update.message.reply_text(f"User with ID {target_user_id} does not exist.")
            return

        # Set permanent VIP status (36500 days = 100 years)
        success = db.set_user_vip_status(target_user_id, True, 36500, permanent=True)

        if success:
            # Send VIP welcome message
            try:
                # Create a welcome message
                welcome_message = f"🎉 <b>Gutlaýarys!</b> 🎉\n\nSize hemişelik VIP statusy berildi! Indi has köp funksiýalara eýe bolduňyz.\n\nKomandalar sanawy bilen tanyşmak üçin /commands komandasyny ulanyp bilersiňiz."

                # Send the message to the user
                context.bot.send_message(
                    chat_id=target_user_id,
                    text=welcome_message,
                    parse_mode='HTML'
                )
            except Exception as e:
                print(f"Error sending VIP welcome message: {e}")

            update.message.reply_text(f"✅ Successfully enabled permanent VIP status for user {target_user_id}.")
            print(f"Set permanent VIP status for user {target_user_id}")
        else:
            update.message.reply_text(f"❌ Failed to update VIP status for user {target_user_id}.")
            print(f"Failed to set VIP status for user {target_user_id}")

    except ValueError:
        update.message.reply_text("Invalid user ID. Please use numbers only.")
    except Exception as e:
        update.message.reply_text(f"Error setting VIP status: {e}")
        print(f"Error setting VIP status: {e}")
