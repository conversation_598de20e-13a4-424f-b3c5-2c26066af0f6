import os
import json
import datetime
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext
from utils.constants import ParseMode
from utils.db import Database
from utils.languages import get_message

# Create directory for profile changes if it doesn't exist
PROFILE_CHANGES_DIR = "profile_changes"
if not os.path.exists(PROFILE_CHANGES_DIR):
    os.makedirs(PROFILE_CHANGES_DIR)

def save_profile_change(user_id, change_type, old_value, new_value):
    """Save profile change to file"""
    # Create user directory if it doesn't exist
    user_dir = os.path.join(PROFILE_CHANGES_DIR, str(user_id))
    if not os.path.exists(user_dir):
        os.makedirs(user_dir)

    # Create change record
    change = {
        "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "change_type": change_type,
        "old_value": old_value,
        "new_value": new_value
    }

    # Save to file
    changes_file = os.path.join(user_dir, f"{change_type}_changes.json")

    # Load existing changes if file exists
    changes = []
    if os.path.exists(changes_file):
        try:
            with open(changes_file, 'r', encoding='utf-8') as f:
                changes = json.load(f)
        except:
            changes = []

    # Add new change
    changes.append(change)

    # Save changes
    with open(changes_file, 'w', encoding='utf-8') as f:
        json.dump(changes, f, ensure_ascii=False, indent=2)

    # Save to all changes file
    all_changes_file = os.path.join(PROFILE_CHANGES_DIR, "all_changes.json")
    all_changes = []

    if os.path.exists(all_changes_file):
        try:
            with open(all_changes_file, 'r', encoding='utf-8') as f:
                all_changes = json.load(f)
        except:
            all_changes = []

    # Add user info to change
    change["user_id"] = user_id
    all_changes.append(change)

    # Save all changes
    with open(all_changes_file, 'w', encoding='utf-8') as f:
        json.dump(all_changes, f, ensure_ascii=False, indent=2)

def can_edit_profile(user_id, change_type):
    """Check if user can edit profile field"""
    # Get database instance
    db = Database()

    # Check if user is admin or VIP
    user_info = db.get_user_info(user_id)
    is_admin = user_id in [123456789]  # Replace with actual admin IDs
    is_vip = user_info.get('is_vip', False)

    # Admins and VIPs can always edit
    if is_admin or is_vip:
        return True

    # Check if user has already edited this field
    user_dir = os.path.join(PROFILE_CHANGES_DIR, str(user_id))
    changes_file = os.path.join(user_dir, f"{change_type}_changes.json")

    if os.path.exists(changes_file):
        try:
            with open(changes_file, 'r', encoding='utf-8') as f:
                changes = json.load(f)
                # If there are changes, user has already edited this field
                return len(changes) == 0
        except:
            # If file is corrupted, allow edit
            return True

    # If file doesn't exist, user hasn't edited this field
    return True

def edit_profile_callback(update: Update, context: CallbackContext):
    """Handle edit profile callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get user info
    user_info = db.get_user_info(user_id)

    # Create profile edit message
    if language == 'ru':
        profile_text = "<b>✏️ Редактирование профиля</b>\n\n"
        profile_text += "Выберите, что вы хотите изменить:"
    elif language == 'en':
        profile_text = "<b>✏️ Edit Profile</b>\n\n"
        profile_text += "Choose what you want to edit:"
    else:
        profile_text = "<b>✏️ Profili üýtgetmek</b>\n\n"
        profile_text += "Näme üýtgetmek isleýäniňizi saýlaň:"

    # Create keyboard
    if language == 'ru':
        keyboard = [
            [InlineKeyboardButton("👤 Имя", callback_data="edit_name")],
            [InlineKeyboardButton("📧 Email", callback_data="edit_email")],
            [InlineKeyboardButton("📱 Телефон", callback_data="edit_phone")],
            [InlineKeyboardButton("📝 О себе", callback_data="edit_bio")],
            [InlineKeyboardButton("🖼 Фото профиля", callback_data="upload_photo")],
            [InlineKeyboardButton("🔙 Назад", callback_data="profile_settings")]
        ]
    elif language == 'en':
        keyboard = [
            [InlineKeyboardButton("👤 Name", callback_data="edit_name")],
            [InlineKeyboardButton("📧 Email", callback_data="edit_email")],
            [InlineKeyboardButton("📱 Phone", callback_data="edit_phone")],
            [InlineKeyboardButton("📝 Bio", callback_data="edit_bio")],
            [InlineKeyboardButton("🖼 Profile Photo", callback_data="upload_photo")],
            [InlineKeyboardButton("🔙 Back", callback_data="profile_settings")]
        ]
    else:
        keyboard = [
            [InlineKeyboardButton("👤 Ady", callback_data="edit_name")],
            [InlineKeyboardButton("📧 Email", callback_data="edit_email")],
            [InlineKeyboardButton("📱 Telefon", callback_data="edit_phone")],
            [InlineKeyboardButton("📝 Özüň barada", callback_data="edit_bio")],
            [InlineKeyboardButton("🖼 Profil suraty", callback_data="upload_photo")],
            [InlineKeyboardButton("🔙 Yza", callback_data="profile_settings")]
        ]

    # Send profile edit message
    query.edit_message_text(
        profile_text,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def edit_name_callback(update: Update, context: CallbackContext):
    """Handle edit name callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get current name
    user_info = db.get_user_info(user_id)
    current_name = user_info.get('full_name', '')

    # Create edit name message
    if language == 'ru':
        name_text = "<b>✏️ Изменить имя</b>\n\n"
        name_text += f"Текущее имя: <b>{current_name}</b>\n\n"
        name_text += "Пожалуйста, отправьте ваше новое имя."
    elif language == 'en':
        name_text = "<b>✏️ Edit Name</b>\n\n"
        name_text += f"Current name: <b>{current_name}</b>\n\n"
        name_text += "Please send your new name."
    else:
        name_text = "<b>✏️ Ady üýtgetmek</b>\n\n"
        name_text += f"Häzirki ady: <b>{current_name}</b>\n\n"
        name_text += "Täze adyňyzy iberiň."

    # Create keyboard
    if language == 'ru':
        keyboard = [[InlineKeyboardButton("🔙 Назад", callback_data="edit_profile")]]
    elif language == 'en':
        keyboard = [[InlineKeyboardButton("🔙 Back", callback_data="edit_profile")]]
    else:
        keyboard = [[InlineKeyboardButton("🔙 Yza", callback_data="edit_profile")]]

    # Set state to await name input
    context.user_data['awaiting_name_input'] = True

    # Send edit name message
    query.edit_message_text(
        name_text,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def handle_name_input(update: Update, context: CallbackContext):
    """Handle name input"""
    user_id = update.effective_user.id
    new_name = update.message.text

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get current name
    user_info = db.get_user_info(user_id)
    current_name = user_info.get('full_name', '')

    # Check if user can edit name
    if not can_edit_profile(user_id, 'name'):
        # User has already edited their name
        if language == 'ru':
            message = "⚠️ Вы уже изменили свое имя. Только VIP пользователи могут изменять профиль несколько раз."
        elif language == 'en':
            message = "⚠️ You have already changed your name. Only VIP users can edit their profile multiple times."
        else:
            message = "⚠️ Siz eýýäm adyňyzy üýtgedipdirsiňiz. Diňe VIP ulanyjylar öz profilini birnäçe gezek üýtgedip bilýärler."

        # Create keyboard
        if language == 'ru':
            keyboard = [[InlineKeyboardButton("🔙 Назад к профилю", callback_data="edit_profile")]]
        elif language == 'en':
            keyboard = [[InlineKeyboardButton("🔙 Back to Profile", callback_data="edit_profile")]]
        else:
            keyboard = [[InlineKeyboardButton("🔙 Profile gaýt", callback_data="edit_profile")]]

        update.message.reply_text(
            message,
            parse_mode=ParseMode.HTML,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

        # Clear state
        context.user_data['awaiting_name_input'] = False
        return

    # Save profile change
    save_profile_change(user_id, 'name', current_name, new_name)

    # Update name in database
    db.update_user_profile(user_id, full_name=new_name)

    # Clear state
    context.user_data['awaiting_name_input'] = False

    # Send confirmation message
    if language == 'ru':
        confirmation = f"✅ Ваше имя успешно изменено на <b>{new_name}</b>."
    elif language == 'en':
        confirmation = f"✅ Your name has been successfully changed to <b>{new_name}</b>."
    else:
        confirmation = f"✅ Adyňyz üstünlikli <b>{new_name}</b> adyna üýtgedildi."

    # Create keyboard
    if language == 'ru':
        keyboard = [[InlineKeyboardButton("🔙 Назад к профилю", callback_data="edit_profile")]]
    elif language == 'en':
        keyboard = [[InlineKeyboardButton("🔙 Back to Profile", callback_data="edit_profile")]]
    else:
        keyboard = [[InlineKeyboardButton("🔙 Profile gaýt", callback_data="edit_profile")]]

    update.message.reply_text(
        confirmation,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def edit_email_callback(update: Update, context: CallbackContext):
    """Handle edit email callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get current email
    user_info = db.get_user_info(user_id)
    current_email = user_info.get('email', '')

    # Create edit email message
    if language == 'ru':
        email_text = "<b>✏️ Изменить Email</b>\n\n"
        email_text += f"Текущий email: <b>{current_email}</b>\n\n"
        email_text += "Пожалуйста, отправьте ваш новый email."
    elif language == 'en':
        email_text = "<b>✏️ Edit Email</b>\n\n"
        email_text += f"Current email: <b>{current_email}</b>\n\n"
        email_text += "Please send your new email."
    else:
        email_text = "<b>✏️ Email üýtgetmek</b>\n\n"
        email_text += f"Häzirki email: <b>{current_email}</b>\n\n"
        email_text += "Täze emailiňizi iberiň."

    # Create keyboard
    if language == 'ru':
        keyboard = [[InlineKeyboardButton("🔙 Назад", callback_data="edit_profile")]]
    elif language == 'en':
        keyboard = [[InlineKeyboardButton("🔙 Back", callback_data="edit_profile")]]
    else:
        keyboard = [[InlineKeyboardButton("🔙 Yza", callback_data="edit_profile")]]

    # Set state to await email input
    context.user_data['awaiting_email_input'] = True

    # Send edit email message
    query.edit_message_text(
        email_text,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def handle_email_input(update: Update, context: CallbackContext):
    """Handle email input"""
    user_id = update.effective_user.id
    new_email = update.message.text

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get current email
    user_info = db.get_user_info(user_id)
    current_email = user_info.get('email', '')

    # Check if user can edit email
    if not can_edit_profile(user_id, 'email'):
        # User has already edited their email
        if language == 'ru':
            message = "⚠️ Вы уже изменили свой email. Только VIP пользователи могут изменять профиль несколько раз."
        elif language == 'en':
            message = "⚠️ You have already changed your email. Only VIP users can edit their profile multiple times."
        else:
            message = "⚠️ Siz eýýäm emailiňizi üýtgedipdirsiňiz. Diňe VIP ulanyjylar öz profilini birnäçe gezek üýtgedip bilýärler."

        # Create keyboard
        if language == 'ru':
            keyboard = [[InlineKeyboardButton("🔙 Назад к профилю", callback_data="edit_profile")]]
        elif language == 'en':
            keyboard = [[InlineKeyboardButton("🔙 Back to Profile", callback_data="edit_profile")]]
        else:
            keyboard = [[InlineKeyboardButton("🔙 Profile gaýt", callback_data="edit_profile")]]

        update.message.reply_text(
            message,
            parse_mode=ParseMode.HTML,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

        # Clear state
        context.user_data['awaiting_email_input'] = False
        return

    # Save profile change
    save_profile_change(user_id, 'email', current_email, new_email)

    # Update email in database
    db.update_user_profile(user_id, email=new_email)

    # Clear state
    context.user_data['awaiting_email_input'] = False

    # Send confirmation message
    if language == 'ru':
        confirmation = f"✅ Ваш email успешно изменен на <b>{new_email}</b>."
    elif language == 'en':
        confirmation = f"✅ Your email has been successfully changed to <b>{new_email}</b>."
    else:
        confirmation = f"✅ Emailiňiz üstünlikli <b>{new_email}</b> adresine üýtgedildi."

    # Create keyboard
    if language == 'ru':
        keyboard = [[InlineKeyboardButton("🔙 Назад к профилю", callback_data="edit_profile")]]
    elif language == 'en':
        keyboard = [[InlineKeyboardButton("🔙 Back to Profile", callback_data="edit_profile")]]
    else:
        keyboard = [[InlineKeyboardButton("🔙 Profile gaýt", callback_data="edit_profile")]]

    update.message.reply_text(
        confirmation,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def edit_phone_callback(update: Update, context: CallbackContext):
    """Handle edit phone callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get current phone
    user_info = db.get_user_info(user_id)
    current_phone = user_info.get('phone_number', '')

    # Create edit phone message
    if language == 'ru':
        phone_text = "<b>✏️ Изменить телефон</b>\n\n"
        phone_text += f"Текущий телефон: <b>{current_phone}</b>\n\n"
        phone_text += "Пожалуйста, отправьте ваш новый номер телефона."
    elif language == 'en':
        phone_text = "<b>✏️ Edit Phone</b>\n\n"
        phone_text += f"Current phone: <b>{current_phone}</b>\n\n"
        phone_text += "Please send your new phone number."
    else:
        phone_text = "<b>✏️ Telefon üýtgetmek</b>\n\n"
        phone_text += f"Häzirki telefon: <b>{current_phone}</b>\n\n"
        phone_text += "Täze telefon belgiňizi iberiň."

    # Create keyboard
    if language == 'ru':
        keyboard = [[InlineKeyboardButton("🔙 Назад", callback_data="edit_profile")]]
    elif language == 'en':
        keyboard = [[InlineKeyboardButton("🔙 Back", callback_data="edit_profile")]]
    else:
        keyboard = [[InlineKeyboardButton("🔙 Yza", callback_data="edit_profile")]]

    # Set state to await phone input
    context.user_data['awaiting_phone_input'] = True

    # Send edit phone message
    query.edit_message_text(
        phone_text,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def handle_phone_input(update: Update, context: CallbackContext):
    """Handle phone input"""
    user_id = update.effective_user.id
    new_phone = update.message.text

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get current phone
    user_info = db.get_user_info(user_id)
    current_phone = user_info.get('phone_number', '')

    # Check if user can edit phone
    if not can_edit_profile(user_id, 'phone'):
        # User has already edited their phone
        if language == 'ru':
            message = "⚠️ Вы уже изменили свой телефон. Только VIP пользователи могут изменять профиль несколько раз."
        elif language == 'en':
            message = "⚠️ You have already changed your phone. Only VIP users can edit their profile multiple times."
        else:
            message = "⚠️ Siz eýýäm telefon belgiňizi üýtgedipdirsiňiz. Diňe VIP ulanyjylar öz profilini birnäçe gezek üýtgedip bilýärler."

        # Create keyboard
        if language == 'ru':
            keyboard = [[InlineKeyboardButton("🔙 Назад к профилю", callback_data="edit_profile")]]
        elif language == 'en':
            keyboard = [[InlineKeyboardButton("🔙 Back to Profile", callback_data="edit_profile")]]
        else:
            keyboard = [[InlineKeyboardButton("🔙 Profile gaýt", callback_data="edit_profile")]]

        update.message.reply_text(
            message,
            parse_mode=ParseMode.HTML,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

        # Clear state
        context.user_data['awaiting_phone_input'] = False
        return

    # Save profile change
    save_profile_change(user_id, 'phone', current_phone, new_phone)

    # Update phone in database
    db.update_user_profile(user_id, phone=new_phone)

    # Clear state
    context.user_data['awaiting_phone_input'] = False

    # Send confirmation message
    if language == 'ru':
        confirmation = f"✅ Ваш телефон успешно изменен на <b>{new_phone}</b>."
    elif language == 'en':
        confirmation = f"✅ Your phone has been successfully changed to <b>{new_phone}</b>."
    else:
        confirmation = f"✅ Telefon belgiňiz üstünlikli <b>{new_phone}</b> belgisine üýtgedildi."

    # Create keyboard
    if language == 'ru':
        keyboard = [[InlineKeyboardButton("🔙 Назад к профилю", callback_data="edit_profile")]]
    elif language == 'en':
        keyboard = [[InlineKeyboardButton("🔙 Back to Profile", callback_data="edit_profile")]]
    else:
        keyboard = [[InlineKeyboardButton("🔙 Profile gaýt", callback_data="edit_profile")]]

    update.message.reply_text(
        confirmation,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def edit_bio_callback(update: Update, context: CallbackContext):
    """Handle edit bio callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get current bio
    user_info = db.get_user_info(user_id)
    current_bio = user_info.get('bio', '')

    # Create edit bio message
    if language == 'ru':
        bio_text = "<b>✏️ Изменить информацию о себе</b>\n\n"
        bio_text += f"Текущая информация: <b>{current_bio}</b>\n\n"
        bio_text += "Пожалуйста, отправьте новую информацию о себе."
    elif language == 'en':
        bio_text = "<b>✏️ Edit Bio</b>\n\n"
        bio_text += f"Current bio: <b>{current_bio}</b>\n\n"
        bio_text += "Please send your new bio."
    else:
        bio_text = "<b>✏️ Özüň barada maglumaty üýtgetmek</b>\n\n"
        bio_text += f"Häzirki maglumat: <b>{current_bio}</b>\n\n"
        bio_text += "Täze maglumaty iberiň."

    # Create keyboard
    if language == 'ru':
        keyboard = [[InlineKeyboardButton("🔙 Назад", callback_data="edit_profile")]]
    elif language == 'en':
        keyboard = [[InlineKeyboardButton("🔙 Back", callback_data="edit_profile")]]
    else:
        keyboard = [[InlineKeyboardButton("🔙 Yza", callback_data="edit_profile")]]

    # Set state to await bio input
    context.user_data['awaiting_bio_input'] = True

    # Send edit bio message
    query.edit_message_text(
        bio_text,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def handle_bio_input(update: Update, context: CallbackContext):
    """Handle bio input"""
    user_id = update.effective_user.id
    new_bio = update.message.text

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get current bio
    user_info = db.get_user_info(user_id)
    current_bio = user_info.get('bio', '')

    # Check if user can edit bio
    if not can_edit_profile(user_id, 'bio'):
        # User has already edited their bio
        if language == 'ru':
            message = "⚠️ Вы уже изменили информацию о себе. Только VIP пользователи могут изменять профиль несколько раз."
        elif language == 'en':
            message = "⚠️ You have already changed your bio. Only VIP users can edit their profile multiple times."
        else:
            message = "⚠️ Siz eýýäm özüňiz baradaky maglumaty üýtgedipdirsiňiz. Diňe VIP ulanyjylar öz profilini birnäçe gezek üýtgedip bilýärler."

        # Create keyboard
        if language == 'ru':
            keyboard = [[InlineKeyboardButton("🔙 Назад к профилю", callback_data="edit_profile")]]
        elif language == 'en':
            keyboard = [[InlineKeyboardButton("🔙 Back to Profile", callback_data="edit_profile")]]
        else:
            keyboard = [[InlineKeyboardButton("🔙 Profile gaýt", callback_data="edit_profile")]]

        update.message.reply_text(
            message,
            parse_mode=ParseMode.HTML,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

        # Clear state
        context.user_data['awaiting_bio_input'] = False
        return

    # Save profile change
    save_profile_change(user_id, 'bio', current_bio, new_bio)

    # Update bio in database
    db.update_user_profile(user_id, bio=new_bio)

    # Clear state
    context.user_data['awaiting_bio_input'] = False

    # Send confirmation message
    if language == 'ru':
        confirmation = "✅ Информация о вас успешно обновлена."
    elif language == 'en':
        confirmation = "✅ Your bio has been successfully updated."
    else:
        confirmation = "✅ Özüňiz baradaky maglumat üstünlikli täzelendi."

    # Create keyboard
    if language == 'ru':
        keyboard = [[InlineKeyboardButton("🔙 Назад к профилю", callback_data="edit_profile")]]
    elif language == 'en':
        keyboard = [[InlineKeyboardButton("🔙 Back to Profile", callback_data="edit_profile")]]
    else:
        keyboard = [[InlineKeyboardButton("🔙 Profile gaýt", callback_data="edit_profile")]]

    update.message.reply_text(
        confirmation,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def upload_photo_callback(update: Update, context: CallbackContext):
    """Handle upload photo callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Create upload photo message
    if language == 'ru':
        photo_text = "<b>🖼 Загрузить фото профиля</b>\n\n"
        photo_text += "Пожалуйста, отправьте новое фото для вашего профиля."
    elif language == 'en':
        photo_text = "<b>🖼 Upload Profile Photo</b>\n\n"
        photo_text += "Please send a new photo for your profile."
    else:
        photo_text = "<b>🖼 Profil suraty ýüklemek</b>\n\n"
        photo_text += "Profiliňiz üçin täze surat iberiň."

    # Create keyboard
    if language == 'ru':
        keyboard = [[InlineKeyboardButton("🔙 Назад", callback_data="edit_profile")]]
    elif language == 'en':
        keyboard = [[InlineKeyboardButton("🔙 Back", callback_data="edit_profile")]]
    else:
        keyboard = [[InlineKeyboardButton("🔙 Yza", callback_data="edit_profile")]]

    # Set state to await photo upload
    context.user_data['awaiting_photo_upload'] = True

    # Send upload photo message
    query.edit_message_text(
        photo_text,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def handle_photo_upload(update: Update, context: CallbackContext):
    """Handle photo upload"""
    user_id = update.effective_user.id
    photo = update.message.photo[-1]  # Get the largest photo

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get current photo
    user_info = db.get_user_info(user_id)
    current_photo = user_info.get('profile_photo', '')

    # Check if user can edit photo
    if not can_edit_profile(user_id, 'photo'):
        # User has already edited their photo
        if language == 'ru':
            message = "⚠️ Вы уже изменили фото профиля. Только VIP пользователи могут изменять профиль несколько раз."
        elif language == 'en':
            message = "⚠️ You have already changed your profile photo. Only VIP users can edit their profile multiple times."
        else:
            message = "⚠️ Siz eýýäm profil suratyňyzy üýtgedipdirsiňiz. Diňe VIP ulanyjylar öz profilini birnäçe gezek üýtgedip bilýärler."

        # Create keyboard
        if language == 'ru':
            keyboard = [[InlineKeyboardButton("🔙 Назад к профилю", callback_data="edit_profile")]]
        elif language == 'en':
            keyboard = [[InlineKeyboardButton("🔙 Back to Profile", callback_data="edit_profile")]]
        else:
            keyboard = [[InlineKeyboardButton("🔙 Profile gaýt", callback_data="edit_profile")]]

        update.message.reply_text(
            message,
            parse_mode=ParseMode.HTML,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

        # Clear state
        context.user_data['awaiting_photo_upload'] = False
        return

    # Get photo file ID
    photo_file_id = photo.file_id

    # Save photo to disk
    try:
        # Create user directory if it doesn't exist
        user_dir = os.path.join(PROFILE_CHANGES_DIR, str(user_id))
        if not os.path.exists(user_dir):
            os.makedirs(user_dir)

        # Get photo file
        photo_file = update.message.photo[-1].get_file()

        # Generate filename with timestamp
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        photo_path = os.path.join(user_dir, f"profile_photo_{timestamp}.jpg")

        # Download photo
        photo_file.download(photo_path)

        # Save profile change
        save_profile_change(user_id, 'photo', current_photo, photo_file_id)
    except Exception as e:
        print(f"Error saving photo: {e}")

    # Update profile photo in database
    db.update_user_profile(user_id, profile_photo=photo_file_id)

    # Clear state
    context.user_data['awaiting_photo_upload'] = False

    # Send confirmation message
    if language == 'ru':
        confirmation = "✅ Фото профиля успешно обновлено."
    elif language == 'en':
        confirmation = "✅ Profile photo has been successfully updated."
    else:
        confirmation = "✅ Profil suraty üstünlikli täzelendi."

    # Create keyboard
    if language == 'ru':
        keyboard = [[InlineKeyboardButton("🔙 Назад к профилю", callback_data="edit_profile")]]
    elif language == 'en':
        keyboard = [[InlineKeyboardButton("🔙 Back to Profile", callback_data="edit_profile")]]
    else:
        keyboard = [[InlineKeyboardButton("🔙 Profile gaýt", callback_data="edit_profile")]]

    update.message.reply_text(
        confirmation,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )
