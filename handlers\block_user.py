from telegram import Update, ParseMode
from telegram.ext import CallbackContext
from utils.db import Database
from config import ADMIN_IDS, OWNER_ID

def is_admin(user_id):
    """Check if a user is an admin"""
    return user_id in ADMIN_IDS

def block_user_command(update: Update, context: CallbackContext):
    """Handle /block_user command to block a user from using the bot"""
    user_id = update.effective_user.id
    
    # Check if user is admin
    if not is_admin(user_id):
        update.message.reply_text("⛔ Bu komanda diňe administratorlar üçin elýeterlidir.")
        return
    
    # Check if target user ID is provided
    if not context.args or len(context.args) < 1:
        update.message.reply_text("❌ Ulanyjy ID-si görkezilmedi. Dogry formatda ýazyň: /block_user [user_id]")
        return
    
    try:
        # Get target user ID
        target_user_id = int(context.args[0])
        
        # Check if user exists
        db = Database()
        user_info = db.get_user_info(target_user_id)
        
        if not user_info:
            update.message.reply_text(f"❌ ID-si {target_user_id} bolan ulanyjy tapylmady.")
            return
        
        # Check if user is already blocked
        if user_info.get('is_blocked', False):
            update.message.reply_text(f"⚠️ ID-si {target_user_id} bolan ulanyjy eýýäm blokirlenipdir.")
            return
        
        # Block the user
        success = db.block_user(target_user_id)
        
        if success:
            # Get user's username or full name for the message
            username = user_info.get('username', '')
            full_name = user_info.get('full_name', '')
            user_display = f"@{username}" if username else full_name if full_name else str(target_user_id)
            
            update.message.reply_text(
                f"✅ Ulanyjy {user_display} (ID: {target_user_id}) üstünlikli blokirlendi.\n\n"
                f"Bu ulanyjy indi boty ulanyp bilmez. Blokirowkany aýyrmak üçin /unblock_user {target_user_id} komandany ulanyň.",
                parse_mode=ParseMode.HTML
            )
            
            # Try to notify the user that they've been blocked
            try:
                context.bot.send_message(
                    chat_id=target_user_id,
                    text="⛔ <b>Duýduryş:</b> Administrator tarapyndan blokirlendiňiz. Indi boty ulanyp bilmersiňiz.\n\n"
                         "Eger bu ýalňyşlyk bolsa, administrator bilen habarlaşyň.",
                    parse_mode=ParseMode.HTML
                )
            except Exception:
                # Ignore errors when sending message to the user
                pass
        else:
            update.message.reply_text(f"❌ Ulanyjyny blokirlemekde ýalňyşlyk ýüze çykdy.")
    
    except ValueError:
        update.message.reply_text("❌ Nädogry ulanyjy ID-si. Diňe sanlar ulanyň.")
    except Exception as e:
        update.message.reply_text(f"❌ Ýalňyşlyk: {e}")

def unblock_user_command(update: Update, context: CallbackContext):
    """Handle /unblock_user command to unblock a user"""
    user_id = update.effective_user.id
    
    # Check if user is admin
    if not is_admin(user_id):
        update.message.reply_text("⛔ Bu komanda diňe administratorlar üçin elýeterlidir.")
        return
    
    # Check if target user ID is provided
    if not context.args or len(context.args) < 1:
        update.message.reply_text("❌ Ulanyjy ID-si görkezilmedi. Dogry formatda ýazyň: /unblock_user [user_id]")
        return
    
    try:
        # Get target user ID
        target_user_id = int(context.args[0])
        
        # Check if user exists
        db = Database()
        user_info = db.get_user_info(target_user_id)
        
        if not user_info:
            update.message.reply_text(f"❌ ID-si {target_user_id} bolan ulanyjy tapylmady.")
            return
        
        # Check if user is already unblocked
        if not user_info.get('is_blocked', False):
            update.message.reply_text(f"⚠️ ID-si {target_user_id} bolan ulanyjy blokirlenmändir.")
            return
        
        # Unblock the user
        success = db.unblock_user(target_user_id)
        
        if success:
            # Get user's username or full name for the message
            username = user_info.get('username', '')
            full_name = user_info.get('full_name', '')
            user_display = f"@{username}" if username else full_name if full_name else str(target_user_id)
            
            update.message.reply_text(
                f"✅ Ulanyjy {user_display} (ID: {target_user_id}) blokirowkasy üstünlikli aýryldy.\n\n"
                f"Bu ulanyjy indi boty ýene ulanyp biler.",
                parse_mode=ParseMode.HTML
            )
            
            # Try to notify the user that they've been unblocked
            try:
                context.bot.send_message(
                    chat_id=target_user_id,
                    text="✅ <b>Habar:</b> Administrator tarapyndan blokirowkaňyz aýryldy. Indi boty ýene ulanyp bilersiňiz.",
                    parse_mode=ParseMode.HTML
                )
            except Exception:
                # Ignore errors when sending message to the user
                pass
        else:
            update.message.reply_text(f"❌ Ulanyjynyň blokirowkasyny aýyrmakda ýalňyşlyk ýüze çykdy.")
    
    except ValueError:
        update.message.reply_text("❌ Nädogry ulanyjy ID-si. Diňe sanlar ulanyň.")
    except Exception as e:
        update.message.reply_text(f"❌ Ýalňyşlyk: {e}")
