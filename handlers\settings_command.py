from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext
from utils.constants import ParseMode
from utils.db import Database
from utils.languages import get_message

def settings_command(update: Update, context: CallbackContext):
    """Handle /settings command"""
    user_id = update.effective_user.id
    
    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)
    
    # Get user settings
    user_info = db.get_user_info(user_id)
    notifications_enabled = user_info.get('notifications_enabled', True)
    theme = user_info.get('theme', 'light')
    
    # Create settings message
    settings_text = f"<b>{get_message('settings_title', language)}</b>\n\n"
    
    # Add language setting
    settings_text += f"🌐 <b>{get_message('language_setting', language)}:</b> "
    if language == 'tm':
        settings_text += "Türkmen 🇹🇲\n"
    elif language == 'ru':
        settings_text += "Русский 🇷🇺\n"
    else:
        settings_text += "English 🇬🇧\n"
    
    # Add theme setting
    settings_text += f"🎨 <b>{get_message('theme_setting', language)}:</b> "
    if theme == 'light':
        settings_text += f"{get_message('theme_light', language)}\n"
    else:
        settings_text += f"{get_message('theme_dark', language)}\n"
    
    # Add notifications setting
    settings_text += f"🔔 <b>{get_message('notification_status', language)}:</b> "
    if notifications_enabled:
        settings_text += f"{get_message('notifications_enabled', language)}\n"
    else:
        settings_text += f"{get_message('notifications_disabled', language)}\n"
    
    # Create keyboard
    keyboard = [
        [InlineKeyboardButton(get_message('language_button', language), callback_data="change_language")],
        [InlineKeyboardButton(get_message('theme_button', language), callback_data="change_theme")]
    ]
    
    # Add notifications toggle button
    if notifications_enabled:
        keyboard.append([InlineKeyboardButton(get_message('disable_notifications', language), callback_data="disable_notifications")])
    else:
        keyboard.append([InlineKeyboardButton(get_message('enable_notifications', language), callback_data="enable_notifications")])
    
    # Add search preferences button
    keyboard.append([InlineKeyboardButton(get_message('search_preferences', language), callback_data="search_preferences")])
    
    # Add back button
    keyboard.append([InlineKeyboardButton(get_message('back', language), callback_data="main_menu")])
    
    # Send settings message
    update.message.reply_text(
        settings_text,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )
