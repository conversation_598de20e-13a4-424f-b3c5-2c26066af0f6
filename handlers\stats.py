from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from utils.constants import ParseMode
from telegram.ext import CallbackContext
from utils.db_stats import StatsDatabase
from utils.keyboards import get_back_button
import datetime

def stats_command(update: Update, context: CallbackContext):
    """Handle /stats command to show detailed statistics"""
    user_id = update.effective_user.id

    # Check if user is admin
    from config import ADMIN_IDS
    if user_id not in ADMIN_IDS:
        update.message.reply_text("⛔ Bu komanda diňe administratorlar üçin elýeterlidir.")
        return

    # Get user's language
    db = StatsDatabase()
    language = db.get_user_language(user_id)

    # Create keyboard for different stats views
    keyboard = [
        [InlineKeyboardButton("👥 Ulanyjy statistikasy", callback_data="stats_users")],
        [InlineKeyboardButton("🔍 Gözleg statistikasy", callback_data="stats_searches")],
        [InlineKeyboardButton("⭐ VIP statistikasy", callback_data="stats_vip")],
        [InlineKeyboardButton("📊 Grafik görnüşinde", callback_data="stats_graph")],
        [InlineKeyboardButton("🔙 Yza", callback_data="admin")]
    ]

    # Send stats menu
    update.message.reply_text(
        "📊 <b>Statistika paneli</b>\n\n"
        "Görmek isleýän statistikany saýlaň:",
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def stats_callback(update: Update, context: CallbackContext):
    """Handle stats callback queries"""
    query = update.callback_query
    user_id = query.from_user.id

    # Check if user is admin
    from config import ADMIN_IDS
    if user_id not in ADMIN_IDS:
        query.answer("⛔ Bu funksiýa diňe administratorlar üçin elýeterlidir.")
        return

    # Get user's language
    db = StatsDatabase()
    language = db.get_user_language(user_id)

    # Get the stats type
    stats_type = query.data.split('_')[1]

    if stats_type == 'users':
        # User statistics
        total_users = db.get_total_users_count()
        active_users = db.get_active_users_count()
        new_users_today = db.get_new_users_count(1)
        new_users_week = db.get_new_users_count(7)
        new_users_month = db.get_new_users_count(30)

        stats_text = (
            "👥 <b>Ulanyjy statistikasy</b>\n\n"
            f"📊 Jemi ulanyjylar: {total_users}\n"
            f"✅ Aktiw ulanyjylar: {active_users}\n"
            f"🆕 Şu gün täze ulanyjylar: {new_users_today}\n"
            f"📆 Şu hepde täze ulanyjylar: {new_users_week}\n"
            f"📅 Şu aý täze ulanyjylar: {new_users_month}\n\n"
            f"📱 Platformalar:\n"
            f"  • Android: {db.get_platform_count('android')}\n"
            f"  • iOS: {db.get_platform_count('ios')}\n"
            f"  • Desktop: {db.get_platform_count('desktop')}\n"
            f"  • Web: {db.get_platform_count('web')}\n"
            f"  • Beýleki: {db.get_platform_count('other')}\n\n"
            f"🌐 Diller:\n"
            f"  • Türkmen: {db.get_language_count('tm')}\n"
            f"  • Rus: {db.get_language_count('ru')}\n"
            f"  • Iňlis: {db.get_language_count('en')}"
        )

        # Create back button
        keyboard = [[InlineKeyboardButton("🔙 Yza", callback_data="stats_menu")]]

        # Send stats
        query.edit_message_text(
            stats_text,
            parse_mode=ParseMode.HTML,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    elif stats_type == 'searches':
        # Search statistics
        total_searches = db.get_total_searches_count()
        searches_today = db.get_searches_count(1)
        searches_week = db.get_searches_count(7)
        searches_month = db.get_searches_count(30)

        stats_text = (
            "🔍 <b>Gözleg statistikasy</b>\n\n"
            f"📊 Jemi gözlegler: {total_searches}\n"
            f"📆 Şu gün gözlegler: {searches_today}\n"
            f"📆 Şu hepde gözlegler: {searches_week}\n"
            f"📅 Şu aý gözlegler: {searches_month}\n\n"
            f"🔎 Gözleg görnüşleri:\n"
            f"  • Telefon belgisi: {db.get_search_type_count('phone')}\n"
            f"  • Ady: {db.get_search_type_count('name')}\n"
            f"  • Pasport: {db.get_search_type_count('passport')}\n"
            f"  • Salgysy: {db.get_search_type_count('address')}\n\n"
            f"⏱️ Ortaça gözleg wagty: {db.get_average_search_time():.2f} sekunt"
        )

        # Create back button
        keyboard = [[InlineKeyboardButton("🔙 Yza", callback_data="stats_menu")]]

        # Send stats
        query.edit_message_text(
            stats_text,
            parse_mode=ParseMode.HTML,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    elif stats_type == 'vip':
        # VIP statistics
        total_vip = db.get_vip_users_count()
        vip_active = db.get_active_vip_users_count()
        vip_expired = db.get_expired_vip_users_count()
        vip_revenue = db.get_vip_revenue()

        stats_text = (
            "⭐ <b>VIP statistikasy</b>\n\n"
            f"👑 Jemi VIP ulanyjylar: {total_vip}\n"
            f"✅ Aktiw VIP ulanyjylar: {vip_active}\n"
            f"❌ Möhleti geçen VIP ulanyjylar: {vip_expired}\n\n"
            f"💰 Jemi girdejiler: {vip_revenue} TMT\n\n"
            f"📊 VIP paketler:\n"
            f"  • 1 aýlyk: {db.get_vip_package_count(1)} sany\n"
            f"  • 3 aýlyk: {db.get_vip_package_count(3)} sany\n"
            f"  • 6 aýlyk: {db.get_vip_package_count(6)} sany\n"
            f"  • 12 aýlyk: {db.get_vip_package_count(12)} sany"
        )

        # Create back button
        keyboard = [[InlineKeyboardButton("🔙 Yza", callback_data="stats_menu")]]

        # Send stats
        query.edit_message_text(
            stats_text,
            parse_mode=ParseMode.HTML,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    elif stats_type == 'graph':
        # Graph statistics (text-based version)
        try:
            # Get user growth data
            user_growth = db.get_user_growth_data()

            # Create a text-based representation
            if user_growth['dates']:
                graph_text = "📈 <b>Ulanyjy ösüşi</b>\n\n"

                # Add data points
                for i in range(len(user_growth['dates'])):
                    date = user_growth['dates'][i]
                    count = user_growth['counts'][i]
                    graph_text += f"{date}: {count} täze ulanyjy\n"

                # Send the text-based graph
                query.edit_message_text(
                    graph_text,
                    parse_mode=ParseMode.HTML,
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Yza", callback_data="stats_menu")]])
                )
            else:
                query.answer("Görkezmek üçin maglumat ýok")

            # Inform user
            query.answer("Maglumat görkezildi!")
        except Exception as e:
            print(f"Error creating graph: {e}")
            query.answer("Grafik döretmekde ýalňyşlyk ýüze çykdy.")

    elif stats_type == 'menu':
        # Return to stats menu
        keyboard = [
            [InlineKeyboardButton("👥 Ulanyjy statistikasy", callback_data="stats_users")],
            [InlineKeyboardButton("🔍 Gözleg statistikasy", callback_data="stats_searches")],
            [InlineKeyboardButton("⭐ VIP statistikasy", callback_data="stats_vip")],
            [InlineKeyboardButton("📊 Grafik görnüşinde", callback_data="stats_graph")],
            [InlineKeyboardButton("🔙 Yza", callback_data="admin")]
        ]

        # Send stats menu
        query.edit_message_text(
            "📊 <b>Statistika paneli</b>\n\n"
            "Görmek isleýän statistikany saýlaň:",
            parse_mode=ParseMode.HTML,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
