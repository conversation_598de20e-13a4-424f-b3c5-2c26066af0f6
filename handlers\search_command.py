from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ParseMode
from telegram.ext import CallbackContext
from utils.db import Database
from utils.languages import get_message
from utils.keyboards import get_search_keyboard

def search_command(update: Update, context: CallbackContext):
    """Handle /search command"""
    user_id = update.effective_user.id
    
    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)
    
    # Check if user has search points
    search_points = db.get_user_search_points(user_id)
    
    if search_points <= 0:
        # User has no search points
        keyboard = [
            [InlineKeyboardButton(get_message('vip_button', language), callback_data="vip_buy")],
            [InlineKeyboardButton(get_message('back', language), callback_data="main_menu")]
        ]
        
        update.message.reply_text(
            get_message('no_points', language),
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
        return
    
    # Show search options
    update.message.reply_text(
        get_message('search_options', language),
        reply_markup=get_search_keyboard(language)
    )
    
    # Check if query was provided with command
    if context.args and len(context.args) > 0:
        # Join all arguments into a single query
        query = ' '.join(context.args)
        
        # Set user data for search handler
        context.user_data['search_query'] = query
        
        # Determine search type based on query
        if query.isdigit() and (query.startswith('993') or query.startswith('63')):
            # Phone number search
            from handlers.search import handle_search_query
            handle_search_query(update, context)
        elif len(query) >= 3:
            # Name or passport search
            from handlers.search import handle_search_query
            handle_search_query(update, context)
        else:
            # Invalid query
            update.message.reply_text(
                "⚠️ " + get_message('invalid_query', language),
                reply_markup=get_search_keyboard(language)
            )
