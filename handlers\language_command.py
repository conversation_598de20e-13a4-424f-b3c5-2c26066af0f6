from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext
from utils.db import Database
from utils.languages import get_message
from utils.keyboards import get_language_keyboard

def language_command(update: Update, context: CallbackContext):
    """Handle /language command"""
    user_id = update.effective_user.id
    
    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)
    
    # Create language selection message
    language_text = get_message('select_new_language', language)
    
    # Send language selection message
    update.message.reply_text(
        language_text,
        reply_markup=get_language_keyboard()
    )

def change_language_command(update: Update, context: CallbackContext):
    """Handle change_language callback query"""
    query = update.callback_query
    user_id = query.from_user.id
    
    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)
    
    # Create language selection message
    language_text = get_message('select_new_language', language)
    
    # Send language selection message
    query.edit_message_text(
        language_text,
        reply_markup=get_language_keyboard()
    )
