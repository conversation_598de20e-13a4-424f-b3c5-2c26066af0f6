from telegram import Update
from utils.constants import ParseMode
from telegram.ext import CallbackContext
from utils.db import Database
from config import OWNER_ID
from handlers.admin_commands import owner_commands_callback

def owner_commands_command(update: Update, context: CallbackContext):
    """Handle /owner_commands command to show all available owner commands"""
    user_id = update.effective_user.id

    # Check if user is owner
    if user_id != OWNER_ID:
        update.message.reply_text("⚠️ Bu komanda diňe bot eýesi üçin elýeterlidir.")
        return

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Create a list of all owner commands with descriptions
    if language == 'ru':
        commands_message = (
            "👑 <b>Команды владельца</b>\n\n"

            "<b>👥 Управление пользователями:</b>\n"
            "🔹 <code>/add_points [user_id] [points]</code> - Добавить баллы пользователю\n"
            "   <i>Пример: /add_points 123456789 5</i>\n"
            "🔹 <code>/remove_points [user_id] [points]</code> - Удалить баллы у пользователя\n"
            "   <i>Пример: /remove_points 123456789 3</i>\n"
            "🔹 <code>/set_vip [user_id] [1 or 0] [days]</code> - Установить постоянный VIP статус пользователю\n"
            "   <i>Пример: /set_vip 123456789 1 (постоянный VIP)</i>\n"
            "🔹 <code>/set_vip_1 [user_id]</code> - Установить VIP статус на 1 месяц (100 баллов)\n"
            "   <i>Пример: /set_vip_1 123456789</i>\n"
            "🔹 <code>/set_vip_3 [user_id]</code> - Установить VIP статус на 3 месяца (300 баллов)\n"
            "   <i>Пример: /set_vip_3 123456789</i>\n"
            "🔹 <code>/set_vip_6 [user_id]</code> - Установить VIP статус на 6 месяцев (1000 баллов)\n"
            "   <i>Пример: /set_vip_6 123456789</i>\n"
            "🔹 <code>/new_admin [user_id]</code> - Назначить нового администратора\n\n"

            "<b>⚙️ Системные команды:</b>\n"
            "🔹 <code>/reload</code> - Перезагрузить данные телефонов\n"
            "🔹 <code>/restart</code> - Перезапустить бота\n"
            "🔹 <code>/status</code> - Проверить статус бота\n"
            "🔹 <code>/broadcast [message]</code> - Отправить сообщение всем пользователям\n"
            "🔹 <code>/search_stats</code> - Просмотр статистики поиска\n\n"

            "👑 <b>Вы владелец бота! У вас есть доступ ко всем функциям.</b>"
        )
    elif language == 'en':
        commands_message = (
            "👑 <b>Owner Commands</b>\n\n"

            "<b>👥 User Management:</b>\n"
            "🔹 <code>/add_points [user_id] [points]</code> - Add points to a user\n"
            "   <i>Example: /add_points 123456789 5</i>\n"
            "🔹 <code>/remove_points [user_id] [points]</code> - Remove points from a user\n"
            "   <i>Example: /remove_points 123456789 3</i>\n"
            "🔹 <code>/set_vip [user_id] [1 or 0] [days]</code> - Set permanent VIP status for a user\n"
            "   <i>Example: /set_vip 123456789 1 (permanent VIP)</i>\n"
            "🔹 <code>/set_vip_1 [user_id]</code> - Set VIP status for 1 month (100 points)\n"
            "   <i>Example: /set_vip_1 123456789</i>\n"
            "🔹 <code>/set_vip_3 [user_id]</code> - Set VIP status for 3 months (300 points)\n"
            "   <i>Example: /set_vip_3 123456789</i>\n"
            "🔹 <code>/set_vip_6 [user_id]</code> - Set VIP status for 6 months (1000 points)\n"
            "   <i>Example: /set_vip_6 123456789</i>\n"
            "🔹 <code>/new_admin [user_id]</code> - Assign a new administrator\n\n"

            "<b>⚙️ System Commands:</b>\n"
            "🔹 <code>/reload</code> - Reload phone data\n"
            "🔹 <code>/restart</code> - Restart the bot\n"
            "🔹 <code>/status</code> - Check bot status\n"
            "🔹 <code>/broadcast [message]</code> - Send a message to all users\n"
            "🔹 <code>/search_stats</code> - View search statistics\n\n"

            "👑 <b>You are the bot owner! You have access to all features.</b>"
        )
    else:
        commands_message = (
            "👑 <b>Eýe Komandalar</b>\n\n"

            "<b>👥 Ulanyjy Dolandyryş:</b>\n"
            "🔹 <code>/add_points [user_id] [points]</code> - Ulanyja bal goşmak\n"
            "   <i>Mysal: /add_points 123456789 5</i>\n"
            "🔹 <code>/remove_points [user_id] [points]</code> - Ulanyja bal aýyrmak\n"
            "   <i>Mysal: /remove_points 123456789 3</i>\n"
            "🔹 <code>/set_vip [user_id] [1 or 0] [days]</code> - Ulanyja hemişelik VIP statusy bermek\n"
            "   <i>Mysal: /set_vip 123456789 1 (hemişelik VIP)</i>\n"
            "🔹 <code>/set_vip_1 [user_id]</code> - Ulanyja 1 aýlyk VIP statusy bermek (100 bal)\n"
            "   <i>Mysal: /set_vip_1 123456789</i>\n"
            "🔹 <code>/set_vip_3 [user_id]</code> - Ulanyja 3 aýlyk VIP statusy bermek (300 bal)\n"
            "   <i>Mysal: /set_vip_3 123456789</i>\n"
            "🔹 <code>/set_vip_6 [user_id]</code> - Ulanyja 6 aýlyk VIP statusy bermek (1000 bal)\n"
            "   <i>Mysal: /set_vip_6 123456789</i>\n"
            "🔹 <code>/new_admin [user_id]</code> - Täze administrator bellemek\n\n"

            "<b>⚙️ Ulgam Komandalary:</b>\n"
            "🔹 <code>/reload</code> - Telefon maglumatlaryny täzelemek\n"
            "🔹 <code>/restart</code> - Boty täzeden başlatmak\n"
            "🔹 <code>/status</code> - Bot ýagdaýyny barlamak\n"
            "🔹 <code>/broadcast [message]</code> - Ähli ullanyjylara habar ibermek\n"
            "🔹 <code>/search_stats</code> - Gözleg statistikasyny görkezýär\n\n"

            "👑 <b>Siz botyň eýesisiniz! Size ähli funksiýalar elýeterlidir.</b>"
        )

    # Send the commands list
    update.message.reply_text(
        commands_message,
        parse_mode=ParseMode.HTML
    )
