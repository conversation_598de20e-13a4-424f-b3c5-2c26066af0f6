from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext
from utils.db import Database
import csv
import pandas as pd
import os
import datetime
from fpdf import FPDF
import qrcode
from io import BytesIO

def export_history_command(update: Update, context: CallbackContext):
    """Handle the /export command"""
    user_id = update.effective_user.id
    
    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)
    
    # Create keyboard with export options
    keyboard = [
        [InlineKeyboardButton("CSV", callback_data="export_csv")],
        [InlineKeyboardButton("Excel", callback_data="export_excel")],
        [InlineKeyboardButton("PDF", callback_data="export_pdf")]
    ]
    
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    # Send message with export options
    if language == 'ru':
        message = "📤 <b>Экспорт истории поиска</b>\n\nВыберите формат для экспорта вашей истории поиска:"
    elif language == 'en':
        message = "📤 <b>Export Search History</b>\n\nSelect a format to export your search history:"
    else:
        message = "📤 <b>Gözleg taryhyňyzy eksport etmek</b>\n\nGözleg taryhyňyzy eksport etmek üçin format saýlaň:"
    
    update.message.reply_text(message, reply_markup=reply_markup, parse_mode='HTML')

def export_history_callback(update: Update, context: CallbackContext):
    """Handle export history callbacks"""
    query = update.callback_query
    user_id = query.from_user.id
    callback_data = query.data
    
    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)
    
    # Get user's search history
    history = db.get_search_history(user_id, limit=100)
    
    if not history:
        if language == 'ru':
            query.answer("У вас нет истории поиска для экспорта.")
        elif language == 'en':
            query.answer("You don't have any search history to export.")
        else:
            query.answer("Eksport etmek üçin gözleg taryhyňyz ýok.")
        return
    
    # Create directory for exports if it doesn't exist
    if not os.path.exists('exports'):
        os.makedirs('exports')
    
    # Generate filename with timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    filename_base = f"exports/search_history_{user_id}_{timestamp}"
    
    # Handle different export formats
    if callback_data == "export_csv":
        export_to_csv(query, history, filename_base, language)
    elif callback_data == "export_excel":
        export_to_excel(query, history, filename_base, language)
    elif callback_data == "export_pdf":
        export_to_pdf(query, history, filename_base, language)

def export_to_csv(query, history, filename_base, language):
    """Export search history to CSV"""
    filename = f"{filename_base}.csv"
    
    try:
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            # Define CSV headers based on language
            if language == 'ru':
                fieldnames = ['Запрос', 'Тип поиска', 'Количество результатов', 'Время поиска']
            elif language == 'en':
                fieldnames = ['Query', 'Search Type', 'Results Count', 'Search Time']
            else:
                fieldnames = ['Gözleg', 'Gözleg görnüşi', 'Netije sany', 'Gözleg wagty']
            
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for item in history:
                writer.writerow({
                    fieldnames[0]: item['query'],
                    fieldnames[1]: item['search_type'],
                    fieldnames[2]: item['results_count'],
                    fieldnames[3]: item['search_time']
                })
        
        # Send the CSV file
        with open(filename, 'rb') as file:
            query.message.reply_document(
                document=file,
                filename=f"search_history_{query.from_user.id}.csv",
                caption="📊 CSV formatynda gözleg taryhyňyz"
            )
        
        # Answer the callback query
        if language == 'ru':
            query.answer("История поиска успешно экспортирована в CSV!")
        elif language == 'en':
            query.answer("Search history successfully exported to CSV!")
        else:
            query.answer("Gözleg taryhy CSV formatyna üstünlikli eksport edildi!")
            
    except Exception as e:
        print(f"Error exporting to CSV: {e}")
        if language == 'ru':
            query.answer("Ошибка при экспорте истории поиска.")
        elif language == 'en':
            query.answer("Error exporting search history.")
        else:
            query.answer("Gözleg taryhyny eksport etmekde ýalňyşlyk.")

def export_to_excel(query, history, filename_base, language):
    """Export search history to Excel"""
    filename = f"{filename_base}.xlsx"
    
    try:
        # Create DataFrame
        if language == 'ru':
            df = pd.DataFrame([{
                'Запрос': item['query'],
                'Тип поиска': item['search_type'],
                'Количество результатов': item['results_count'],
                'Время поиска': item['search_time']
            } for item in history])
        elif language == 'en':
            df = pd.DataFrame([{
                'Query': item['query'],
                'Search Type': item['search_type'],
                'Results Count': item['results_count'],
                'Search Time': item['search_time']
            } for item in history])
        else:
            df = pd.DataFrame([{
                'Gözleg': item['query'],
                'Gözleg görnüşi': item['search_type'],
                'Netije sany': item['results_count'],
                'Gözleg wagty': item['search_time']
            } for item in history])
        
        # Save to Excel
        df.to_excel(filename, index=False)
        
        # Send the Excel file
        with open(filename, 'rb') as file:
            query.message.reply_document(
                document=file,
                filename=f"search_history_{query.from_user.id}.xlsx",
                caption="📊 Excel formatynda gözleg taryhyňyz"
            )
        
        # Answer the callback query
        if language == 'ru':
            query.answer("История поиска успешно экспортирована в Excel!")
        elif language == 'en':
            query.answer("Search history successfully exported to Excel!")
        else:
            query.answer("Gözleg taryhy Excel formatyna üstünlikli eksport edildi!")
            
    except Exception as e:
        print(f"Error exporting to Excel: {e}")
        if language == 'ru':
            query.answer("Ошибка при экспорте истории поиска.")
        elif language == 'en':
            query.answer("Error exporting search history.")
        else:
            query.answer("Gözleg taryhyny eksport etmekde ýalňyşlyk.")

def export_to_pdf(query, history, filename_base, language):
    """Export search history to PDF"""
    filename = f"{filename_base}.pdf"
    
    try:
        # Create PDF
        pdf = FPDF()
        pdf.add_page()
        
        # Add title
        pdf.set_font("Arial", "B", 16)
        if language == 'ru':
            pdf.cell(0, 10, "История поиска", ln=True, align="C")
        elif language == 'en':
            pdf.cell(0, 10, "Search History", ln=True, align="C")
        else:
            pdf.cell(0, 10, "Gözleg taryhy", ln=True, align="C")
        
        pdf.ln(10)
        
        # Add table headers
        pdf.set_font("Arial", "B", 12)
        if language == 'ru':
            headers = ["Запрос", "Тип поиска", "Результаты", "Время"]
        elif language == 'en':
            headers = ["Query", "Search Type", "Results", "Time"]
        else:
            headers = ["Gözleg", "Görnüşi", "Netijeler", "Wagty"]
        
        # Calculate column widths
        col_width = pdf.w / 4.5
        
        # Add headers
        for header in headers:
            pdf.cell(col_width, 10, header, border=1)
        pdf.ln()
        
        # Add data
        pdf.set_font("Arial", "", 10)
        for item in history:
            # Format time
            try:
                search_time = datetime.datetime.strptime(item['search_time'], '%Y-%m-%d %H:%M:%S')
                formatted_time = search_time.strftime('%d-%m-%Y %H:%M')
            except:
                formatted_time = item['search_time']
            
            pdf.cell(col_width, 10, str(item['query']), border=1)
            pdf.cell(col_width, 10, str(item['search_type']), border=1)
            pdf.cell(col_width, 10, str(item['results_count']), border=1)
            pdf.cell(col_width, 10, formatted_time, border=1)
            pdf.ln()
        
        # Add QR code with bot link
        pdf.ln(10)
        pdf.set_font("Arial", "B", 12)
        if language == 'ru':
            pdf.cell(0, 10, "Сканируйте QR-код, чтобы открыть бота:", ln=True)
        elif language == 'en':
            pdf.cell(0, 10, "Scan QR code to open the bot:", ln=True)
        else:
            pdf.cell(0, 10, "Boty açmak üçin QR kody skanirläň:", ln=True)
        
        # Generate QR code
        from config import BOT_USERNAME
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(f"https://t.me/{BOT_USERNAME.replace('@', '')}")
        qr.make(fit=True)
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Save QR code to temporary file
        qr_path = f"{filename_base}_qr.png"
        img.save(qr_path)
        
        # Add QR code to PDF
        pdf.image(qr_path, x=pdf.w/2 - 25, y=pdf.h - 70, w=50)
        
        # Add timestamp
        pdf.set_font("Arial", "I", 8)
        pdf.set_y(pdf.h - 10)
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        pdf.cell(0, 10, f"Generated: {timestamp}", ln=True, align="C")
        
        # Save PDF
        pdf.output(filename)
        
        # Send the PDF file
        with open(filename, 'rb') as file:
            query.message.reply_document(
                document=file,
                filename=f"search_history_{query.from_user.id}.pdf",
                caption="📊 PDF formatynda gözleg taryhyňyz"
            )
        
        # Clean up temporary QR code file
        if os.path.exists(qr_path):
            os.remove(qr_path)
        
        # Answer the callback query
        if language == 'ru':
            query.answer("История поиска успешно экспортирована в PDF!")
        elif language == 'en':
            query.answer("Search history successfully exported to PDF!")
        else:
            query.answer("Gözleg taryhy PDF formatyna üstünlikli eksport edildi!")
            
    except Exception as e:
        print(f"Error exporting to PDF: {e}")
        if language == 'ru':
            query.answer("Ошибка при экспорте истории поиска.")
        elif language == 'en':
            query.answer("Error exporting search history.")
        else:
            query.answer("Gözleg taryhyny eksport etmekde ýalňyşlyk.")