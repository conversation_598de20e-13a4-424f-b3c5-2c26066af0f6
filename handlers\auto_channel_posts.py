"""
Automatic channel posting module for sending updates to Telegram channel
"""
import logging
from telegram import <PERSON><PERSON>
from utils.constants import ParseMode
from utils.db import Database
import datetime
import json
import os
from config import CHANNEL_ID, TOKEN
from handlers.search_history_analysis import analyze_search_history

# Set up logging
logging.basicConfig(format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO)
logger = logging.getLogger(__name__)

# Check if channel notifications are enabled
try:
    CHANNEL_ENABLED = bool(CHANNEL_ID)
except (ImportError, AttributeError):
    logger.warning("Channel ID not found in config. Channel posts will be disabled.")
    CHANNEL_ENABLED = False

def send_channel_post(message, bot=None):
    """Send a post to the channel"""
    if not CHANNEL_ENABLED:
        logger.warning("Channel posts are disabled.")
        return False
    
    try:
        # If bot is not provided, create a new one
        if bot is None:
            bot = Bot(TOKEN)
        
        # Send message to channel
        bot.send_message(
            chat_id=CHANNEL_ID,
            text=message,
            parse_mode=ParseMode.HTML
        )
        
        logger.info(f"Post sent to channel: {message[:50]}...")
        return True
    except Exception as e:
        logger.error(f"Error sending post to channel: {e}")
        return False

def post_new_features(feature_name, feature_description, bot=None):
    """Post about new features to the channel"""
    if not CHANNEL_ENABLED:
        return False
    
    try:
        # Create message
        message = f"🆕 <b>Täze funksiýa: {feature_name}</b>\n\n"
        message += f"{feature_description}\n\n"
        message += f"Ulanmak üçin: /start komandasyny ulanyň"
        
        # Send message to channel
        return send_channel_post(message, bot)
    except Exception as e:
        logger.error(f"Error posting new feature: {e}")
        return False

def post_special_offer(offer_title, offer_description, expiry_date=None, bot=None):
    """Post special offers to the channel"""
    if not CHANNEL_ENABLED:
        return False
    
    try:
        # Create message
        message = f"🔥 <b>Ýörite teklip: {offer_title}</b>\n\n"
        message += f"{offer_description}\n\n"
        
        if expiry_date:
            message += f"Möhleti: {expiry_date}\n\n"
            
        message += f"Peýdalanmak üçin: /vip komandasyny ulanyň"
        
        # Send message to channel
        return send_channel_post(message, bot)
    except Exception as e:
        logger.error(f"Error posting special offer: {e}")
        return False

def post_weekly_stats(context=None):
    """Post weekly statistics to the channel"""
    if not CHANNEL_ENABLED:
        return False
    
    try:
        # Get database instance
        db = Database()
        
        # Get current date
        current_date = datetime.datetime.now()
        week_ago = current_date - datetime.timedelta(days=7)
        
        # Format dates
        current_date_str = current_date.strftime('%Y-%m-%d')
        week_ago_str = week_ago.strftime('%Y-%m-%d')
        
        # Get statistics
        total_users = db.get_total_users_count()
        new_users_week = len(db.get_new_users(days=7))
        
        # Get search analysis
        analysis = analyze_search_history(days=7)
        
        # Create message
        message = f"📊 <b>Hepdelik statistika ({week_ago_str} - {current_date_str})</b>\n\n"
        message += f"👥 Jemi ulanyjylar: {total_users}\n"
        message += f"🆕 Täze ulanyjylar: {new_users_week}\n"
        message += f"🔍 Jemi gözlegler: {analysis['total_searches']}\n\n"
        
        # Add most common queries
        if analysis['most_common_queries']:
            message += f"🔝 <b>Iň köp gözlenenler:</b>\n"
            for i, item in enumerate(analysis['most_common_queries'][:5], 1):
                message += f"{i}. {item['query']} - {item['count']} gezek\n"
        
        # Send message to channel
        bot = context.bot if context else None
        return send_channel_post(message, bot)
    except Exception as e:
        logger.error(f"Error posting weekly stats: {e}")
        return False

def post_tips_and_tricks(tip_number, tip_title, tip_content, bot=None):
    """Post tips and tricks to the channel"""
    if not CHANNEL_ENABLED:
        return False
    
    try:
        # Create message
        message = f"💡 <b>Maslahat #{tip_number}: {tip_title}</b>\n\n"
        message += f"{tip_content}\n\n"
        message += f"Has köp maglumat üçin: /help komandasyny ulanyň"
        
        # Send message to channel
        return send_channel_post(message, bot)
    except Exception as e:
        logger.error(f"Error posting tip: {e}")
        return False

def setup_auto_channel_posts(job_queue):
    """Set up jobs for automatic channel posts"""
    if not CHANNEL_ENABLED:
        logger.warning("Channel posts are disabled. Jobs will not be scheduled.")
        return
    
    # Schedule weekly stats post every Monday at 10:00
    job_queue.run_daily(
        post_weekly_stats,
        datetime.time(hour=10, minute=0, second=0),
        days=(0,)  # Monday
    )
    logger.info("Weekly stats post job scheduled")
    
    # You can add more scheduled posts here