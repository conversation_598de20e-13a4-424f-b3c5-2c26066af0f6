from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from utils.constants import ParseMode
from telegram.ext import CallbackContext
from utils.db import Database
from utils.keyboards import get_history_keyboard, get_favorites_keyboard, get_back_button, get_theme_keyboard
from utils.languages import get_message
from handlers.search import format_search_result

def view_history_callback(update: Update, context: CallbackContext):
    """Handle view history callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get user's search history
    history = db.get_search_history(user_id)

    if not history:
        # No history found
        query.edit_message_text(
            get_message('no_history', language),
            reply_markup=get_back_button("main_menu", language)
        )
        return

    # Format history
    history_text = get_message('history_title', language) + "\n\n"

    for i, item in enumerate(history[:10], 1):  # Show only the last 10 searches
        search_time = item['search_time']
        query_text = item['query']
        search_type = item['search_type']
        results_count = item['results_count']

        # Format search type
        if search_type == 'phone':
            type_text = "📱"
        elif search_type == 'name':
            type_text = "👤"
        elif search_type == 'passport':
            type_text = "📗"
        else:
            type_text = "🔍"

        history_text += f"{i}. {type_text} {query_text} - {results_count} results ({search_time})\n"

    # Show history
    query.edit_message_text(
        history_text,
        reply_markup=get_history_keyboard(language)
    )

def view_favorites_callback(update: Update, context: CallbackContext):
    """Handle view favorites callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get user's favorites
    favorites = db.get_user_favorites(user_id)

    if not favorites:
        # No favorites found
        query.edit_message_text(
            get_message('no_favorites', language),
            reply_markup=get_back_button("main_menu", language)
        )
        return

    # Format favorites
    favorites_text = get_message('favorites_title', language) + "\n\n"

    # Store favorites in context for later use
    context.user_data['favorites'] = favorites

    # Show only first favorite with navigation buttons
    current_favorite = 0
    favorite = favorites[current_favorite]

    # Format the favorite as a search result
    formatted_result = format_search_result(favorite, language)

    # Create navigation keyboard
    keyboard = []

    # Add navigation buttons if there are multiple favorites
    if len(favorites) > 1:
        nav_buttons = []
        if current_favorite > 0:
            nav_buttons.append(InlineKeyboardButton("⬅️ Previous", callback_data=f"prev_favorite_{current_favorite-1}"))
        if current_favorite < len(favorites) - 1:
            nav_buttons.append(InlineKeyboardButton("Next ➡️", callback_data=f"next_favorite_{current_favorite+1}"))
        keyboard.append(nav_buttons)

    # Add delete button and back button
    keyboard.append([InlineKeyboardButton(get_message('delete_favorite', language),
                                         callback_data=f"delete_favorite_{current_favorite}")])
    keyboard.append([InlineKeyboardButton(get_message('back', language), callback_data="main_menu")])

    # Show favorite
    query.edit_message_text(
        f"{favorites_text}\n{formatted_result}",
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode=ParseMode.HTML
    )

def navigate_favorites_callback(update: Update, context: CallbackContext):
    """Handle navigation between favorites"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get the direction and index from callback data
    data = query.data
    if data.startswith("next_favorite_"):
        current_favorite = int(data.split("_")[-1])
    elif data.startswith("prev_favorite_"):
        current_favorite = int(data.split("_")[-1])
    else:
        # Invalid callback data
        query.answer("Invalid navigation")
        return

    # Get favorites from context
    favorites = context.user_data.get('favorites', [])

    if not favorites or current_favorite >= len(favorites):
        # No favorites or invalid index
        query.answer("No more favorites")
        return

    # Get the current favorite
    favorite = favorites[current_favorite]

    # Format favorites
    favorites_text = get_message('favorites_title', language) + "\n\n"

    # Format the favorite as a search result
    formatted_result = format_search_result(favorite, language)

    # Create navigation keyboard
    keyboard = []

    # Add navigation buttons if there are multiple favorites
    if len(favorites) > 1:
        nav_buttons = []
        if current_favorite > 0:
            nav_buttons.append(InlineKeyboardButton("⬅️ Previous", callback_data=f"prev_favorite_{current_favorite-1}"))
        if current_favorite < len(favorites) - 1:
            nav_buttons.append(InlineKeyboardButton("Next ➡️", callback_data=f"next_favorite_{current_favorite+1}"))
        keyboard.append(nav_buttons)

    # Add delete button and back button
    keyboard.append([InlineKeyboardButton(get_message('delete_favorite', language),
                                         callback_data=f"delete_favorite_{current_favorite}")])
    keyboard.append([InlineKeyboardButton(get_message('back', language), callback_data="main_menu")])

    # Show favorite
    query.edit_message_text(
        f"{favorites_text}\n{formatted_result}",
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode=ParseMode.HTML
    )

def delete_favorite_callback(update: Update, context: CallbackContext):
    """Handle delete favorite callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get the index from callback data
    data = query.data
    if data.startswith("delete_favorite_"):
        favorite_index = int(data.split("_")[-1])
    else:
        # Invalid callback data
        query.answer("Invalid deletion request")
        return

    # Get favorites from context
    favorites = context.user_data.get('favorites', [])

    if not favorites or favorite_index >= len(favorites):
        # No favorites or invalid index
        query.answer("No favorite to delete")
        return

    # Get the favorite to delete
    favorite = favorites[favorite_index]

    # Ask for confirmation before deleting
    confirm_keyboard = [
        [InlineKeyboardButton("✅ Hawa, poz", callback_data=f"confirm_delete_favorite_{favorite_index}")],
        [InlineKeyboardButton("❌ Ýok, pozma", callback_data=f"cancel_delete_favorite_{favorite_index}")]
    ]

    query.edit_message_text(
        "⚠️ Siz çyndan hem bu belligi pozmak isleýärsiňizmi?",
        reply_markup=InlineKeyboardMarkup(confirm_keyboard)
    )

def confirm_delete_favorite_callback(update: Update, context: CallbackContext):
    """Handle confirmation of favorite deletion"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get the index from callback data
    data = query.data
    if data.startswith("confirm_delete_favorite_"):
        favorite_index = int(data.split("_")[-1])
    elif data.startswith("cancel_delete_favorite_"):
        # User canceled deletion
        query.answer("Pozulmagy bes edildi")
        # Return to favorites view
        query.data = f"next_favorite_{data.split('_')[-1]}"
        navigate_favorites_callback(update, context)
        return
    else:
        # Invalid callback data
        query.answer("Invalid request")
        return

    # Get favorites from context
    favorites = context.user_data.get('favorites', [])

    if not favorites or favorite_index >= len(favorites):
        # No favorites or invalid index
        query.answer("No favorite to delete")
        return

    # Delete from database
    db.delete_favorite(user_id, favorites[favorite_index])

    # Remove from context
    favorites.pop(favorite_index)
    context.user_data['favorites'] = favorites

    # Show confirmation
    query.answer("Bellik pozuldy")

    # If there are more favorites, show the next one
    if favorites:
        # Adjust index if needed
        if favorite_index >= len(favorites):
            favorite_index = len(favorites) - 1

        # Call navigate_favorites to show the next favorite
        query.data = f"next_favorite_{favorite_index}"
        navigate_favorites_callback(update, context)
    else:
        # No more favorites
        query.edit_message_text(
            get_message('no_favorites', language),
            reply_markup=get_back_button("main_menu", language)
        )

def clear_history_callback(update: Update, context: CallbackContext):
    """Handle clear history callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Ask for confirmation before clearing
    confirm_keyboard = [
        [InlineKeyboardButton("✅ Hawa, arassala", callback_data="confirm_clear_history")],
        [InlineKeyboardButton("❌ Ýok, arassalama", callback_data="cancel_clear_history")]
    ]

    query.edit_message_text(
        "⚠️ Siz çyndan hem gözleg taryhyňyzy arassalamak isleýärsiňizmi?",
        reply_markup=InlineKeyboardMarkup(confirm_keyboard)
    )

def confirm_clear_history_callback(update: Update, context: CallbackContext):
    """Handle confirmation of history clearing"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    if query.data == "confirm_clear_history":
        # Clear history from database
        db.clear_search_history(user_id)

        # Show confirmation
        query.answer("Gözleg taryhy arassalandy")

        # Show empty history
        query.edit_message_text(
            get_message('no_history', language),
            reply_markup=get_back_button("main_menu", language)
        )
    else:  # cancel_clear_history
        # User canceled clearing
        query.answer("Arassalamak bes edildi")
        # Return to history view
        view_history_callback(update, context)

def change_theme_callback(update: Update, context: CallbackContext):
    """Handle theme change callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Show theme options
    query.edit_message_text(
        get_message('theme_button', language),
        reply_markup=get_theme_keyboard(language)
    )

def set_theme_callback(update: Update, context: CallbackContext):
    """Handle theme selection callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get the selected theme
    theme = query.data.replace("theme_", "")

    # Save theme preference to database
    db.set_user_theme(user_id, theme)

    # Apply theme to Telegram app
    if theme == "light":
        # Set light theme
        query.answer("Açyk tema saýlandy")
        # Use a direct link to switch to light theme
        context.bot.send_message(
            chat_id=user_id,
            text="Açyk tema saýlandy! Telegram temasyny üýtgetmek üçin şu ýere basyň: tg://settings/themes"
        )
    else:  # dark theme
        # Set dark theme
        query.answer("Garaňky tema saýlandy")
        # Use a direct link to switch to dark theme
        context.bot.send_message(
            chat_id=user_id,
            text="Garaňky tema saýlandy! Telegram temasyny üýtgetmek üçin şu ýere basyň: tg://settings/themes"
        )

    # Return to profile
    from handlers.profile import profile_callback
    profile_callback(update, context)
