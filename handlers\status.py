from telegram import Update
from telegram.ext import CallbackContext
import datetime
import platform
import os

def status_command(update: Update, context: CallbackContext):
    """Handle /status command to show bot and system status"""
    # Get user's ID
    user_id = update.effective_user.id

    # Check if user is admin
    from config import ADMIN_IDS
    if user_id not in ADMIN_IDS:
        update.message.reply_text("⛔ Bu komanda diňe administratorlar üçin elýeterlidir.")
        return

    try:
        # Get system info
        system_info = platform.system()
        python_version = platform.python_version()

        # Get memory usage (simplified without psutil)
        memory_usage = "N/A (psutil not available)"

        # Get CPU usage (simplified without psutil)
        cpu_usage = "N/A (psutil not available)"

        # Get uptime (simplified without psutil)
        uptime = "N/A (psutil not available)"

        # Get database stats
        from utils.db import Database
        db = Database()
        total_users = db.get_total_users_count()
        vip_users = db.get_vip_users_count()
        daily_searches = db.get_daily_searches_count()

        # Format the status message
        status_message = (
            "📊 <b>Bot Status Report</b>\n\n"
            f"🤖 <b>Bot Info:</b>\n"
            f"  • Total Users: {total_users}\n"
            f"  • VIP Users: {vip_users}\n"
            f"  • Daily Searches: {daily_searches}\n\n"
            f"💻 <b>System Info:</b>\n"
            f"  • OS: {system_info}\n"
            f"  • Python: {python_version}\n"
            f"  • Memory Usage: {memory_usage}\n"
            f"  • CPU Usage: {cpu_usage}\n"
            f"  • System Uptime: {uptime}\n\n"
            f"⏰ <b>Current Time:</b> {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )

        # Send the status message
        update.message.reply_text(status_message, parse_mode='HTML')
    except Exception as e:
        update.message.reply_text(f"❌ Error getting status: {e}")
