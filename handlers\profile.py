from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from utils.constants import ParseMode
from telegram.ext import CallbackContext
from utils.db import Database
from utils.keyboards import get_back_button, get_theme_keyboard
from utils.languages import get_message
from config import BOT_USERNAME

def profile_callback(update: Update, context: CallbackContext):
    """Handle profile view callback"""
    try:
        query = update.callback_query
        user_id = query.from_user.id
        username = query.from_user.username or user_id

        # Get user data from database
        db = Database()

        try:
            user_data = db.get_user_info(user_id)
            language = db.get_user_language(user_id)

            if not user_data:
                query.edit_message_text(
                    "Profile not found. Please try again.",
                    reply_markup=get_back_button("main_menu", language)
                )
                return

            # Get user stats
            is_vip = user_data.get('is_vip', False)
            vip_expiry_date = user_data.get('vip_expiry_date', None)
            searches_today = user_data.get('searches_today', 0)

            try:
                total_searches = db.get_total_searches(user_id)  # Use the new method to get accurate total searches
            except Exception as e:
                print(f"Error getting total searches: {e}")
                total_searches = 0

            search_points = user_data.get('search_points', 0)

            try:
                referrals = db.get_referral_count(user_id)
            except Exception as e:
                print(f"Error getting referral count: {e}")
                referrals = 0

            # Get recent search history count
            try:
                recent_searches = len(db.get_search_history(user_id, 10))
            except Exception as e:
                print(f"Error getting recent searches: {e}")
                recent_searches = 0

            # Create referral link
            referral_link = f"https://t.me/{BOT_USERNAME.replace('@', '')}?start={user_id}"

            # Format profile text
            profile_text = get_message('profile_title', language).format(username=f"@{username}")

            # Format VIP status text
            vip_status_text = ""
            if is_vip and vip_expiry_date:
                try:
                    # Convert to datetime object if it's a string
                    if isinstance(vip_expiry_date, str):
                        import datetime
                        vip_expiry_date = datetime.datetime.strptime(vip_expiry_date, '%Y-%m-%d %H:%M:%S')

                    # Calculate days remaining
                    now = datetime.datetime.now()
                    days_remaining = (vip_expiry_date - now).days

                    if days_remaining > 0:
                        vip_status_text = get_message('vip_status_active', language).format(days=days_remaining)
                    else:
                        vip_status_text = get_message('vip_status_expired', language)
                except Exception as e:
                    print(f"Error formatting VIP expiry date: {e}")
                    vip_status_text = get_message('vip_status_active', language).format(days="?")
            elif is_vip:
                vip_status_text = get_message('vip_status_active_no_expiry', language)
            else:
                vip_status_text = get_message('vip_status_inactive', language)

            # Get phone number
            phone_number = user_data.get('phone_number', '')

            # Always set phone as verified
            phone_verified = True
            if language == 'ru':
                phone_verified_text = "✅ Да"
            elif language == 'en':
                phone_verified_text = "✅ Yes"
            else:  # Default to Turkmen
                phone_verified_text = "✅ Hawa"

            # Add VIP status, phone verification and recent searches to profile info
            profile_info = get_message('profile_info_extended', language).format(
                user_id=user_id,
                total_searches=total_searches,
                recent_searches=recent_searches,
                referrals=referrals,
                search_points=search_points,
                vip_status=vip_status_text,
                referral_link=referral_link,
                phone_number=phone_number,
                phone_verified=phone_verified_text
            )

            # Create keyboard with settings button
            keyboard = [
                [InlineKeyboardButton(get_message('vip_button', language), callback_data="vip_buy")],
                [InlineKeyboardButton(get_message('history_button', language), callback_data="view_history"),
                 InlineKeyboardButton(get_message('favorites_button', language), callback_data="view_favorites")],
                [InlineKeyboardButton(get_message('settings_button', language), callback_data="settings")],
                [InlineKeyboardButton(get_message('back', language), callback_data="main_menu")]
            ]

            # Send profile info
            query.edit_message_text(
                f"{profile_text}\n\n{profile_info}",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
        except Exception as e:
            print(f"Error in profile_callback: {e}")
            query.edit_message_text(
                "Error loading profile. Please try again later.",
                reply_markup=get_back_button("main_menu", "tm")
            )
    except Exception as e:
        print(f"Critical error in profile_callback: {e}")