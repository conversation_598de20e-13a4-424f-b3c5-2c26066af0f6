from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from utils.constants import ParseMode
from telegram.ext import CallbackContext
from utils.db import Database
from utils.keyboards import get_back_button
from utils.languages import get_message
from handlers.search import format_search_result, search_results_cache
import tempfile
import os

def bulk_search_command(update: Update, context: CallbackContext):
    """Handle /bulk_search command to search multiple phone numbers at once"""
    user_id = update.effective_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Check if user is VIP
    user_info = db.get_user_info(user_id)
    is_vip = user_info.get('is_vip', False)

    if not is_vip:
        # Only VIP users can use bulk search
        if language == 'ru':
            update.message.reply_text(
                "⭐ Массовый поиск доступен только для VIP пользователей.\n\n"
                "Приобретите VIP статус, чтобы использовать эту функцию.",
                reply_markup=get_back_button("main_menu", language)
            )
        elif language == 'en':
            update.message.reply_text(
                "⭐ Bulk search is only available for VIP users.\n\n"
                "Purchase VIP status to use this feature.",
                reply_markup=get_back_button("main_menu", language)
            )
        else:
            update.message.reply_text(
                "⭐ Köpçülikleýin gözleg diňe VIP ulanyjylar üçin elýeterlidir.\n\n"
                "Bu funksiýany ulanmak üçin VIP statusyny satyn alyň.",
                reply_markup=get_back_button("main_menu", language)
            )
        return

    # Provide instructions
    if language == 'ru':
        update.message.reply_text(
            "📱 <b>Массовый поиск номеров</b>\n\n"
            "Отправьте список номеров телефонов (по одному в строке).\n"
            "Максимум 10 номеров за один раз.\n\n"
            "Пример:\n"
            "<code>99361234567\n"
            "99365555555\n"
            "99312345678</code>",
            parse_mode=ParseMode.HTML,
            reply_markup=get_back_button("main_menu", language)
        )
    elif language == 'en':
        update.message.reply_text(
            "📱 <b>Bulk Phone Number Search</b>\n\n"
            "Send a list of phone numbers (one per line).\n"
            "Maximum 10 numbers at once.\n\n"
            "Example:\n"
            "<code>99361234567\n"
            "99365555555\n"
            "99312345678</code>",
            parse_mode=ParseMode.HTML,
            reply_markup=get_back_button("main_menu", language)
        )
    else:
        update.message.reply_text(
            "📱 <b>Köpçülikleýin telefon belgisi gözlegi</b>\n\n"
            "Telefon belgileriniň sanawyny iberiň (her setirde bir belgi).\n"
            "Bir gezekde iň köp 10 sany belgi.\n\n"
            "Mysal:\n"
            "<code>99361234567\n"
            "99365555555\n"
            "99312345678</code>",
            parse_mode=ParseMode.HTML,
            reply_markup=get_back_button("main_menu", language)
        )

    # Set state to await bulk search input
    context.user_data['awaiting_bulk_search'] = True

def handle_bulk_search_input(update: Update, context: CallbackContext):
    """Handle bulk search input"""
    if not context.user_data.get('awaiting_bulk_search'):
        return False

    user_id = update.effective_user.id
    text = update.message.text.strip()

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Clear the awaiting state
    context.user_data['awaiting_bulk_search'] = False

    # Split the input by lines
    phone_numbers = [line.strip() for line in text.split('\n') if line.strip()]

    # Limit to 10 numbers
    if len(phone_numbers) > 10:
        if language == 'ru':
            update.message.reply_text(
                "⚠️ Слишком много номеров. Максимум 10 номеров за один раз.",
                reply_markup=get_back_button("main_menu", language)
            )
        elif language == 'en':
            update.message.reply_text(
                "⚠️ Too many numbers. Maximum 10 numbers at once.",
                reply_markup=get_back_button("main_menu", language)
            )
        else:
            update.message.reply_text(
                "⚠️ Gaty köp belgi. Bir gezekde iň köp 10 sany belgi.",
                reply_markup=get_back_button("main_menu", language)
            )
        return True

    # Check if there are any numbers
    if not phone_numbers:
        if language == 'ru':
            update.message.reply_text(
                "⚠️ Не найдено номеров телефонов в вашем сообщении.",
                reply_markup=get_back_button("main_menu", language)
            )
        elif language == 'en':
            update.message.reply_text(
                "⚠️ No phone numbers found in your message.",
                reply_markup=get_back_button("main_menu", language)
            )
        else:
            update.message.reply_text(
                "⚠️ Siziň habarynyzda telefon belgisi tapylmady.",
                reply_markup=get_back_button("main_menu", language)
            )
        return True

    # Notify user that search is in progress
    if language == 'ru':
        status_message = update.message.reply_text("🔍 Выполняется поиск... Это может занять некоторое время.")
    elif language == 'en':
        status_message = update.message.reply_text("🔍 Searching... This may take a moment.")
    else:
        status_message = update.message.reply_text("🔍 Gözleg geçirilýär... Bu biraz wagt alyp biler.")

    # Perform search for each number
    all_results = []
    found_count = 0

    for phone in phone_numbers:
        # Clean the phone number
        phone = phone.replace('+', '').replace(' ', '').replace('-', '')

        # Skip invalid numbers
        if not phone.isdigit():
            continue

        # Search for the phone number
        results = db.search_phone_number(phone, 'phone')

        if results:
            found_count += 1
            all_results.extend(results)

    # Cache the results
    if all_results:
        search_results_cache[user_id] = all_results

    # Create a summary message
    if language == 'ru':
        summary = f"📊 <b>Результаты массового поиска</b>\n\n"
        summary += f"🔍 Искали: {len(phone_numbers)} номеров\n"
        summary += f"✅ Найдено: {found_count} номеров\n"
        summary += f"📋 Всего результатов: {len(all_results)}\n\n"
    elif language == 'en':
        summary = f"📊 <b>Bulk Search Results</b>\n\n"
        summary += f"🔍 Searched: {len(phone_numbers)} numbers\n"
        summary += f"✅ Found: {found_count} numbers\n"
        summary += f"📋 Total results: {len(all_results)}\n\n"
    else:
        summary = f"📊 <b>Köpçülikleýin gözleg netijeleri</b>\n\n"
        summary += f"🔍 Gözlenildi: {len(phone_numbers)} sany belgi\n"
        summary += f"✅ Tapyldy: {found_count} sany belgi\n"
        summary += f"📋 Jemi netijeler: {len(all_results)}\n\n"

    # Delete the status message
    try:
        status_message.delete()
    except:
        pass

    # If no results found
    if not all_results:
        if language == 'ru':
            update.message.reply_text(
                f"{summary}❌ Результаты не найдены для указанных номеров.",
                parse_mode=ParseMode.HTML,
                reply_markup=get_back_button("main_menu", language)
            )
        elif language == 'en':
            update.message.reply_text(
                f"{summary}❌ No results found for the provided numbers.",
                parse_mode=ParseMode.HTML,
                reply_markup=get_back_button("main_menu", language)
            )
        else:
            update.message.reply_text(
                f"{summary}❌ Berlen belgiler üçin netije tapylmady.",
                parse_mode=ParseMode.HTML,
                reply_markup=get_back_button("main_menu", language)
            )
        return True

    # Send summary and ask if user wants to export results
    keyboard = [
        [InlineKeyboardButton("📄 TXT", callback_data="bulk_export_txt")],
        [InlineKeyboardButton("📊 CSV", callback_data="bulk_export_csv")],
        [InlineKeyboardButton(get_message('back', language), callback_data="main_menu")]
    ]

    if language == 'ru':
        update.message.reply_text(
            f"{summary}Выберите формат для экспорта результатов:",
            parse_mode=ParseMode.HTML,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
    elif language == 'en':
        update.message.reply_text(
            f"{summary}Choose a format to export the results:",
            parse_mode=ParseMode.HTML,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
    else:
        update.message.reply_text(
            f"{summary}Netijeleri eksport etmek üçin format saýlaň:",
            parse_mode=ParseMode.HTML,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    return True

def bulk_export_callback(update: Update, context: CallbackContext):
    """Handle bulk export callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get results from cache
    results = search_results_cache.get(user_id, [])

    if not results:
        query.answer("No results to export")
        return

    export_format = query.data.split('_')[-1]

    try:
        # Create a temporary file
        fd, path = tempfile.mkstemp(suffix=f'.{export_format}')

        if export_format == 'txt':
            # Export as TXT
            with os.fdopen(fd, 'w', encoding='utf-8') as file:
                # Write header
                if language == 'ru':
                    file.write("РЕЗУЛЬТАТЫ МАССОВОГО ПОИСКА\n\n")
                elif language == 'en':
                    file.write("BULK SEARCH RESULTS\n\n")
                else:
                    file.write("KÖPÇÜLIKLEÝIN GÖZLEG NETIJELERI\n\n")

                # Write data
                for i, result in enumerate(results, 1):
                    if language == 'ru':
                        file.write(f"РЕЗУЛЬТАТ #{i}\n")
                    elif language == 'en':
                        file.write(f"RESULT #{i}\n")
                    else:
                        file.write(f"NETIJE #{i}\n")

                    file.write(f"Телефон/Phone: {result.get('phone_number', 'N/A')}\n")
                    file.write(f"Имя/Name: {result.get('full_name', 'N/A')}\n")
                    file.write(f"Адрес/Address: {result.get('address', 'N/A')}\n")
                    file.write(f"Паспорт/Passport: {result.get('passport', 'N/A')}\n")
                    file.write(f"Дата рождения/Birth: {result.get('birth_info', 'N/A')}\n")
                    file.write(f"SIM ID: {result.get('sim_id', 'N/A')}\n")
                    file.write("\n" + "-"*40 + "\n\n")

        elif export_format == 'csv':
            # Export as CSV (simple version without csv module)
            with os.fdopen(fd, 'w', encoding='utf-8') as file:
                # Write header
                file.write("Phone,Name,Address,Passport,Birth Info,SIM ID\n")

                # Write data
                for result in results:
                    file.write(f"{result.get('phone_number', '')},{result.get('full_name', '')},{result.get('address', '')},{result.get('passport', '')},{result.get('birth_info', '')},{result.get('sim_id', '')}\n")

        # Send the file
        with open(path, 'rb') as file:
            import datetime
            current_time = datetime.datetime.now().strftime("%Y-%m-%d")
            filename = f"bulk_search_results_{current_time}.{export_format}"

            if language == 'ru':
                caption = f"Результаты массового поиска ({len(results)} записей)"
            elif language == 'en':
                caption = f"Bulk search results ({len(results)} records)"
            else:
                caption = f"Köpçülikleýin gözleg netijeleri ({len(results)} ýazgy)"

            query.bot.send_document(
                chat_id=user_id,
                document=file,
                filename=filename,
                caption=caption
            )

        # Inform user
        query.answer("Export completed!")

    except Exception as e:
        print(f"Error exporting bulk results: {e}")
        query.answer("Error during export")
    finally:
        # Clean up
        try:
            os.unlink(path)
        except:
            pass
