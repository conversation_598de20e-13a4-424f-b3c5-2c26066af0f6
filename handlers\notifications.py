# Import telegram modules with try/except to avoid IDE warnings
try:
    from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
    from telegram.ext import CallbackContext
    from utils.constants import ParseMode
except ImportError:
    # Define placeholder classes for type hints
    class Update: pass
    class CallbackContext: pass
    class InlineKeyboardButton: pass
    class InlineKeyboardMarkup: pass
    class ParseMode:
        HTML = "HTML"

from utils.db import Database
from utils.keyboards import get_back_button
from utils.languages import get_message
from config import ADMIN_IDS
from handlers.admin import is_admin

def notifications_command(update: Update, context: CallbackContext):
    """Handle /notifications command"""
    user_id = update.effective_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get unread notifications count
    unread_count = db.get_unread_notifications_count(user_id)

    # Create keyboard
    keyboard = [
        [InlineKeyboardButton("📬 Bildirişleri gör", callback_data="view_notifications")],
        [InlineKeyboardButton("⬅️ Yza", callback_data="main_menu")]
    ]

    # Add admin button if user is admin
    if is_admin(user_id):
        keyboard.insert(0, [InlineKeyboardButton("➕ Täze bildiriş döret", callback_data="create_notification")])

    # Prepare message based on language
    if language == 'ru':
        title = "📬 Уведомления"
        if unread_count > 0:
            message = f"У вас есть {unread_count} непрочитанных уведомлений."
        else:
            message = "У вас нет непрочитанных уведомлений."
    elif language == 'en':
        title = "📬 Notifications"
        if unread_count > 0:
            message = f"You have {unread_count} unread notifications."
        else:
            message = "You have no unread notifications."
    else:  # Default to Turkmen
        title = "📬 Bildirişler"
        if unread_count > 0:
            message = f"Siziň {unread_count} sany okalmedik bildirişiňiz bar."
        else:
            message = "Siziň okalmedik bildirişiňiz ýok."

    # Send message
    update.message.reply_text(
        f"{title}\n\n{message}",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def notifications_callback(update: Update, context: CallbackContext):
    """Handle notifications-related callbacks"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    if query.data == "view_notifications":
        # Get user's notifications
        notifications = db.get_user_notifications(user_id)

        if not notifications:
            # No notifications message based on language
            if language == 'ru':
                no_notifications_msg = "У вас нет уведомлений."
            elif language == 'en':
                no_notifications_msg = "You have no notifications."
            else:
                no_notifications_msg = "Siziň bildirişiňiz ýok."

            keyboard = [[InlineKeyboardButton("⬅️ Yza", callback_data="main_menu")]]

            query.edit_message_text(
                no_notifications_msg,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
            return

        # Format notifications list based on language
        if language == 'ru':
            title = "📬 <b>Ваши уведомления:</b>\n\n"
            read_status = {True: "✓ Прочитано", False: "🆕 Непрочитано"}
        elif language == 'en':
            title = "📬 <b>Your notifications:</b>\n\n"
            read_status = {True: "✓ Read", False: "🆕 Unread"}
        else:  # Default to Turkmen
            title = "📬 <b>Siziň bildirişleriňiz:</b>\n\n"
            read_status = {True: "✓ Okaldy", False: "🆕 Okalmady"}

        message = title

        # Create keyboard with notification buttons
        keyboard = []

        for i, notification in enumerate(notifications[:10], 1):  # Limit to 10 notifications
            # Format notification title with read status
            status_icon = "✓" if notification['is_read'] else "🆕"
            notification_title = notification['title']

            # Add button for each notification
            keyboard.append([InlineKeyboardButton(
                f"{status_icon} {notification_title}",
                callback_data=f"notification_{notification['id']}"
            )])

            # Mark all as read button
            if any(not n['is_read'] for n in notifications):
                if language == 'ru':
                    mark_all_text = "✓ Отметить все как прочитанные"
                elif language == 'en':
                    mark_all_text = "✓ Mark all as read"
                else:
                    mark_all_text = "✓ Hemmesini okaldy diýip bellikle"

                keyboard.append([InlineKeyboardButton(mark_all_text, callback_data="mark_all_read")])

        # Back button and close notifications button
        back_button = "⬅️ Yza"
        close_button = "🔕 Bildirişleri ýap"
        if language == 'ru':
            back_button = "⬅️ Назад"
            close_button = "🔕 Закрыть уведомления"
        elif language == 'en':
            back_button = "⬅️ Back"
            close_button = "🔕 Close notifications"

        keyboard.append([
            InlineKeyboardButton(back_button, callback_data="main_menu"),
            InlineKeyboardButton(close_button, callback_data="disable_notifications")
        ])

        query.edit_message_text(
            message,
            parse_mode=ParseMode.HTML,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    elif query.data.startswith("notification_"):
        # Get notification ID
        notification_id = int(query.data.split("_")[1])

        # Get notification details
        notifications = db.get_user_notifications(user_id)
        notification = next((n for n in notifications if n['id'] == notification_id), None)

        if not notification:
            query.answer("Bildiriş tapylmady.")
            return

        # Mark notification as read
        db.mark_notification_as_read(user_id, notification_id)

        # Format notification details based on language
        if language == 'ru':
            created_label = "Создано"
            expires_label = "Истекает"
            never_label = "Никогда"
            back_label = "Назад к уведомлениям"
        elif language == 'en':
            created_label = "Created"
            expires_label = "Expires"
            never_label = "Never"
            back_label = "Back to notifications"
        else:  # Default to Turkmen
            created_label = "Döredilen"
            expires_label = "Möhleti gutarýar"
            never_label = "Hiç haçan"
            back_label = "Bildirişlere yza"

        # Format message
        message = f"<b>{notification['title']}</b>\n\n"
        message += f"{notification['message']}\n\n"
        message += f"{created_label}: {notification['created_at']}\n"

        if notification['expires_at']:
            message += f"{expires_label}: {notification['expires_at']}\n"
        else:
            message += f"{expires_label}: {never_label}\n"

        # Create keyboard
        keyboard = [
            [InlineKeyboardButton(back_label, callback_data="view_notifications")],
            [InlineKeyboardButton("⬅️ Yza", callback_data="main_menu")]
        ]

        query.edit_message_text(
            message,
            parse_mode=ParseMode.HTML,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    elif query.data == "mark_all_read":
        # Get all unread notifications
        notifications = db.get_user_notifications(user_id)
        unread_notifications = [n for n in notifications if not n['is_read']]

        # Mark all as read
        for notification in unread_notifications:
            db.mark_notification_as_read(user_id, notification['id'])

        # Confirmation message based on language
        if language == 'ru':
            confirmation = f"✓ {len(unread_notifications)} уведомлений отмечены как прочитанные."
        elif language == 'en':
            confirmation = f"✓ {len(unread_notifications)} notifications marked as read."
        else:
            confirmation = f"✓ {len(unread_notifications)} sany bildiriş okaldy diýip belliklendi."

        query.answer(confirmation)

        # Refresh notifications view
        query.data = "view_notifications"
        notifications_callback(update, context)

    elif query.data == "disable_notifications":
        # Disable notifications for user
        db.set_user_notification_preference(user_id, False)

        # Confirmation message based on language
        if language == 'ru':
            confirmation = "🔕 Уведомления отключены."
            message = "Вы отключили уведомления. Вы можете включить их снова в настройках."
        elif language == 'en':
            confirmation = "🔕 Notifications disabled."
            message = "You have disabled notifications. You can enable them again in settings."
        else:
            confirmation = "🔕 Bildirişler ýapyldy."
            message = "Siz bildirişleri ýapdyňyz. Olary gaýtadan sazlamalarda açyp bilersiňiz."

        query.answer(confirmation)

        # Return to main menu with message
        keyboard = [[InlineKeyboardButton("⬅️ Yza", callback_data="main_menu")]]

        query.edit_message_text(
            message,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    elif query.data == "create_notification":
        # Check if user is admin
        if not is_admin(user_id):
            query.answer("Bu funksiýa diňe adminler üçin elýeterlidir.")
            return

        # Show notification creation form
        if language == 'ru':
            help_text = (
                "📝 <b>Создание уведомления</b>\n\n"
                "Отправьте сообщение в следующем формате:\n\n"
                "<code>/create_notification ЗАГОЛОВОК | СООБЩЕНИЕ | ТИП | СРОК_ДНЕЙ</code>\n\n"
                "Пример:\n"
                "<code>/create_notification Новая функция | Мы добавили новую функцию поиска! | feature | 7</code>\n\n"
                "ТИП может быть: feature, system, promo\n"
                "СРОК_ДНЕЙ: Количество дней до истечения срока действия уведомления (0 для бессрочного)"
            )
        elif language == 'en':
            help_text = (
                "📝 <b>Create Notification</b>\n\n"
                "Send a message in this format:\n\n"
                "<code>/create_notification TITLE | MESSAGE | TYPE | EXPIRY_DAYS</code>\n\n"
                "Example:\n"
                "<code>/create_notification New Feature | We've added a new search feature! | feature | 7</code>\n\n"
                "TYPE can be: feature, system, promo\n"
                "EXPIRY_DAYS: Number of days until the notification expires (0 for no expiry)"
            )
        else:  # Default to Turkmen
            help_text = (
                "📝 <b>Bildiriş döretmek</b>\n\n"
                "Şu formatda habar iberiň:\n\n"
                "<code>/create_notification BAŞLYK | HABAR | GÖRNÜŞ | MÖHLET_GÜNLER</code>\n\n"
                "Mysal:\n"
                "<code>/create_notification Täze funksiýa | Biz täze gözleg funksiýasyny goşduk! | feature | 7</code>\n\n"
                "GÖRNÜŞ şular bolup biler: feature, system, promo\n"
                "MÖHLET_GÜNLER: Bildirişiň möhletiniň gutarmagyna çenli günleriň sany (0 - möhletsiz)"
            )

        keyboard = [[InlineKeyboardButton("⬅️ Yza", callback_data="admin_panel")]]

        query.edit_message_text(
            help_text,
            parse_mode=ParseMode.HTML,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

        # Set state to await notification creation
        context.user_data['awaiting_notification_creation'] = True

def handle_create_notification(update: Update, context: CallbackContext):
    """Handle notification creation command"""
    user_id = update.effective_user.id
    message_text = update.message.text

    # Check if user is admin
    if not is_admin(user_id):
        update.message.reply_text("Bu funksiýa diňe adminler üçin elýeterlidir.")
        return

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Parse command
    if not message_text.startswith('/create_notification '):
        # Error message based on language
        if language == 'ru':
            error_msg = "Неверный формат. Используйте: /create_notification ЗАГОЛОВОК | СООБЩЕНИЕ | ТИП | СРОК_ДНЕЙ"
        elif language == 'en':
            error_msg = "Invalid format. Use: /create_notification TITLE | MESSAGE | TYPE | EXPIRY_DAYS"
        else:
            error_msg = "Nädogry format. Ulanyň: /create_notification BAŞLYK | HABAR | GÖRNÜŞ | MÖHLET_GÜNLER"

        update.message.reply_text(error_msg)
        return

    # Remove command part
    content = message_text[len('/create_notification '):].strip()

    # Split by pipe character
    parts = content.split('|')

    if len(parts) < 3:
        # Error message based on language
        if language == 'ru':
            error_msg = "Неверный формат. Используйте: /create_notification ЗАГОЛОВОК | СООБЩЕНИЕ | ТИП | СРОК_ДНЕЙ"
        elif language == 'en':
            error_msg = "Invalid format. Use: /create_notification TITLE | MESSAGE | TYPE | EXPIRY_DAYS"
        else:
            error_msg = "Nädogry format. Ulanyň: /create_notification BAŞLYK | HABAR | GÖRNÜŞ | MÖHLET_GÜNLER"

        update.message.reply_text(error_msg)
        return

    # Extract parts
    title = parts[0].strip()
    message = parts[1].strip()
    notification_type = parts[2].strip().lower()

    # Validate notification type
    valid_types = ['feature', 'system', 'promo']
    if notification_type not in valid_types:
        # Error message based on language
        if language == 'ru':
            error_msg = f"Неверный тип уведомления. Допустимые типы: {', '.join(valid_types)}"
        elif language == 'en':
            error_msg = f"Invalid notification type. Valid types: {', '.join(valid_types)}"
        else:
            error_msg = f"Nädogry bildiriş görnüşi. Dogry görnüşler: {', '.join(valid_types)}"

        update.message.reply_text(error_msg)
        return

    # Get expiry days if provided
    expires_days = None
    if len(parts) >= 4:
        try:
            expires_days_val = int(parts[3].strip())
            expires_days = expires_days_val if expires_days_val > 0 else None
        except ValueError:
            # Error message based on language
            if language == 'ru':
                error_msg = "Срок действия должен быть числом."
            elif language == 'en':
                error_msg = "Expiry days must be a number."
            else:
                error_msg = "Möhlet günleri san bolmaly."

            update.message.reply_text(error_msg)
            return

    # Create notification
    success, notification_id = db.create_notification(title, message, notification_type, user_id, expires_days)

    if success:
        # Success message based on language
        if language == 'ru':
            success_msg = f"✅ Уведомление успешно создано (ID: {notification_id})."
        elif language == 'en':
            success_msg = f"✅ Notification created successfully (ID: {notification_id})."
        else:
            success_msg = f"✅ Bildiriş üstünlikli döredildi (ID: {notification_id})."

        update.message.reply_text(success_msg)
    else:
        # Error message based on language
        if language == 'ru':
            error_msg = "❌ Ошибка при создании уведомления."
        elif language == 'en':
            error_msg = "❌ Error creating notification."
        else:
            error_msg = "❌ Bildiriş döretmekde ýalňyşlyk ýüze çykdy."

        update.message.reply_text(error_msg)

def admin_notifications(update: Update, context: CallbackContext):
    """Handle admin notifications panel"""
    query = update.callback_query
    user_id = query.from_user.id

    # Check if user is admin
    if not is_admin(user_id):
        query.answer("Bu funksiýa diňe adminler üçin elýeterlidir.")
        return

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get all active notifications
    notifications = db.get_active_notifications()

    # Format notifications list based on language
    if language == 'ru':
        title = "📬 <b>Управление уведомлениями</b>\n\n"
        create_button = "➕ Создать уведомление"
        no_notifications = "Нет активных уведомлений."
        back_button = "⬅️ Назад"
    elif language == 'en':
        title = "📬 <b>Notification Management</b>\n\n"
        create_button = "➕ Create Notification"
        no_notifications = "No active notifications."
        back_button = "⬅️ Back"
    else:  # Default to Turkmen
        title = "📬 <b>Bildiriş dolandyryş paneli</b>\n\n"
        create_button = "➕ Bildiriş döret"
        no_notifications = "Işjeň bildirişler ýok."
        back_button = "⬅️ Yza"

    message = title

    if not notifications:
        message += no_notifications
    else:
        # Add notification count
        if language == 'ru':
            message += f"Активных уведомлений: {len(notifications)}\n\n"
        elif language == 'en':
            message += f"Active notifications: {len(notifications)}\n\n"
        else:
            message += f"Işjeň bildirişler: {len(notifications)}\n\n"

    # Create keyboard
    keyboard = [
        [InlineKeyboardButton(create_button, callback_data="create_notification")]
    ]

    # Add buttons for each notification (up to 5)
    for notification in notifications[:5]:
        keyboard.append([InlineKeyboardButton(
            f"🗑️ {notification['title']}",
            callback_data=f"delete_notification_{notification['id']}"
        )])

    # Add back button
    keyboard.append([InlineKeyboardButton(back_button, callback_data="admin_panel")])

    query.edit_message_text(
        message,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def delete_notification_callback(update: Update, context: CallbackContext):
    """Handle notification deletion"""
    query = update.callback_query
    user_id = query.from_user.id

    # Check if user is admin
    if not is_admin(user_id):
        query.answer("Bu funksiýa diňe adminler üçin elýeterlidir.")
        return

    # Get notification ID
    notification_id = int(query.data.split("_")[2])

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Delete notification
    success = db.delete_notification(notification_id)

    if success:
        # Success message based on language
        if language == 'ru':
            query.answer("✅ Уведомление удалено.")
        elif language == 'en':
            query.answer("✅ Notification deleted.")
        else:
            query.answer("✅ Bildiriş pozuldy.")
    else:
        # Error message based on language
        if language == 'ru':
            query.answer("❌ Ошибка при удалении уведомления.")
        elif language == 'en':
            query.answer("❌ Error deleting notification.")
        else:
            query.answer("❌ Bildirişi pozmakda ýalňyşlyk ýüze çykdy.")

    # Refresh notifications panel
    admin_notifications(update, context)

def add_notification_count_to_menu(keyboard, user_id):
    """Add notification count to main menu button if there are unread notifications"""
    db = Database()
    unread_count = db.get_unread_notifications_count(user_id)
    language = db.get_user_language(user_id)

    if unread_count > 0:
        # Find the settings button and add notification count
        for row in keyboard:
            for i, button in enumerate(row):
                if isinstance(button, InlineKeyboardButton) and button.callback_data == "profile_settings":
                    # Replace with new button that includes notification count
                    if language == 'ru':
                        row[i] = InlineKeyboardButton(f"⚙️ Настройки 📬({unread_count})", callback_data="profile_settings")
                    elif language == 'en':
                        row[i] = InlineKeyboardButton(f"⚙️ Settings 📬({unread_count})", callback_data="profile_settings")
                    else:
                        row[i] = InlineKeyboardButton(f"⚙️ Sazlamalar 📬({unread_count})", callback_data="profile_settings")
                    return keyboard

    return keyboard
