"""
Automatic statistics generation and update module
"""
import os
import json
import datetime
import matplotlib.pyplot as plt
from telegram.ext import CallbackContext
from utils.db import Database
import logging

# Set up logging
logging.basicConfig(format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO)
logger = logging.getLogger(__name__)

def generate_daily_stats(context: CallbackContext):
    """Generate daily statistics and save them to a file"""
    logger.info("Generating daily statistics...")
    
    # Create stats directory if it doesn't exist
    if not os.path.exists('stats'):
        os.makedirs('stats')
    
    # Get database instance
    db = Database()
    
    # Get current date
    current_date = datetime.datetime.now().strftime('%Y-%m-%d')
    
    # Collect statistics
    stats = {
        'date': current_date,
        'total_users': db.get_total_users_count(),
        'vip_users': db.get_vip_users_count(),
        'daily_searches': db.get_daily_searches_count(),
        'new_users_today': len(db.get_new_users(days=1)),
        'top_searches': db.get_most_searched_queries(limit=10)
    }
    
    # Save statistics to JSON file
    stats_file = f'stats/daily_stats_{current_date}.json'
    with open(stats_file, 'w', encoding='utf-8') as f:
        json.dump(stats, f, ensure_ascii=False, indent=4)
    
    # Generate graphs
    generate_user_growth_graph()
    generate_search_activity_graph()
    
    logger.info(f"Daily statistics generated and saved to {stats_file}")
    
    # If context is provided, send notification to admin
    if context and hasattr(context, 'bot'):
        from config import OWNER_ID
        try:
            context.bot.send_message(
                chat_id=OWNER_ID,
                text=f"📊 Gündelik statistika täzelendi!\n\n"
                     f"📅 Sene: {current_date}\n"
                     f"👥 Jemi ulanyjylar: {stats['total_users']}\n"
                     f"⭐ VIP ulanyjylar: {stats['vip_users']}\n"
                     f"🔍 Şu günki gözlegler: {stats['daily_searches']}\n"
                     f"🆕 Şu günki täze ulanyjylar: {stats['new_users_today']}"
            )
        except Exception as e:
            logger.error(f"Error sending stats notification: {e}")

def generate_user_growth_graph():
    """Generate a graph showing user growth over time"""
    try:
        # Create stats directory if it doesn't exist
        if not os.path.exists('stats'):
            os.makedirs('stats')
        
        # Create graphs directory if it doesn't exist
        if not os.path.exists('stats/graphs'):
            os.makedirs('stats/graphs')
        
        # Get all daily stats files
        stats_files = [f for f in os.listdir('stats') if f.startswith('daily_stats_') and f.endswith('.json')]
        stats_files.sort()  # Sort by date
        
        # Extract dates and user counts
        dates = []
        user_counts = []
        vip_counts = []
        
        for stats_file in stats_files:
            try:
                with open(f'stats/{stats_file}', 'r', encoding='utf-8') as f:
                    stats = json.load(f)
                    dates.append(stats['date'])
                    user_counts.append(stats['total_users'])
                    vip_counts.append(stats['vip_users'])
            except Exception as e:
                logger.error(f"Error reading stats file {stats_file}: {e}")
        
        # If we have at least 2 data points, generate the graph
        if len(dates) >= 2:
            plt.figure(figsize=(10, 6))
            plt.plot(dates, user_counts, marker='o', linestyle='-', color='blue', label='Jemi ulanyjylar')
            plt.plot(dates, vip_counts, marker='s', linestyle='-', color='gold', label='VIP ulanyjylar')
            plt.title('Ulanyjy ösüşi')
            plt.xlabel('Sene')
            plt.ylabel('Ulanyjylaryň sany')
            plt.xticks(rotation=45)
            plt.grid(True)
            plt.legend()
            plt.tight_layout()
            
            # Save the graph
            graph_path = 'stats/graphs/user_growth.png'
            plt.savefig(graph_path)
            plt.close()
            
            logger.info(f"User growth graph generated and saved to {graph_path}")
    except Exception as e:
        logger.error(f"Error generating user growth graph: {e}")

def generate_search_activity_graph():
    """Generate a graph showing search activity over time"""
    try:
        # Create stats directory if it doesn't exist
        if not os.path.exists('stats'):
            os.makedirs('stats')
        
        # Create graphs directory if it doesn't exist
        if not os.path.exists('stats/graphs'):
            os.makedirs('stats/graphs')
        
        # Get all daily stats files
        stats_files = [f for f in os.listdir('stats') if f.startswith('daily_stats_') and f.endswith('.json')]
        stats_files.sort()  # Sort by date
        
        # Extract dates and search counts
        dates = []
        search_counts = []
        
        for stats_file in stats_files:
            try:
                with open(f'stats/{stats_file}', 'r', encoding='utf-8') as f:
                    stats = json.load(f)
                    dates.append(stats['date'])
                    search_counts.append(stats['daily_searches'])
            except Exception as e:
                logger.error(f"Error reading stats file {stats_file}: {e}")
        
        # If we have at least 2 data points, generate the graph
        if len(dates) >= 2:
            plt.figure(figsize=(10, 6))
            plt.plot(dates, search_counts, marker='o', linestyle='-', color='green')
            plt.title('Gözleg işjeňligi')
            plt.xlabel('Sene')
            plt.ylabel('Gözlegleriň sany')
            plt.xticks(rotation=45)
            plt.grid(True)
            plt.tight_layout()
            
            # Save the graph
            graph_path = 'stats/graphs/search_activity.png'
            plt.savefig(graph_path)
            plt.close()
            
            logger.info(f"Search activity graph generated and saved to {graph_path}")
    except Exception as e:
        logger.error(f"Error generating search activity graph: {e}")

def setup_daily_stats_job(job_queue):
    """Set up a job to generate daily statistics"""
    # Run at midnight every day
    job_queue.run_daily(
        generate_daily_stats,
        datetime.time(hour=0, minute=0, second=0)
    )
    logger.info("Daily statistics job scheduled")
    
    # Also run once at startup to ensure we have today's stats
    generate_daily_stats(None)
    logger.info("Initial statistics generated")
