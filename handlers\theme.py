from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from utils.constants import ParseMode
from telegram.ext import CallbackContext
from utils.db import Database
from utils.languages import get_message

def change_theme_callback(update: Update, context: CallbackContext):
    """Handle theme change callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get current theme
    user_data = db.get_user_info(user_id)
    current_theme = user_data.get('theme', 'light')

    # Create theme selection message
    theme_text = f"<b>🎨 {get_message('theme_setting', language)}</b>\n\n"
    theme_text += f"{get_message('current_theme', language)}: "

    if current_theme == 'light':
        theme_text += f"{get_message('theme_light', language)}"
    else:
        theme_text += f"{get_message('theme_dark', language)}"

    # Create keyboard
    keyboard = [
        [InlineKeyboardButton("☀️ " + get_message('theme_light', language), callback_data="set_theme_light")],
        [InlineKeyboardButton("🌙 " + get_message('theme_dark', language), callback_data="set_theme_dark")],
        [InlineKeyboardButton("🔄 " + get_message('theme_auto', language), callback_data="set_theme_auto")],
        [InlineKeyboardButton(get_message('back', language), callback_data="profile_settings")]
    ]

    # Send theme selection message
    query.edit_message_text(
        theme_text,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def set_theme_callback(update: Update, context: CallbackContext):
    """Handle setting theme callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Set theme based on callback data
    if query.data == "set_theme_light":
        theme = "light"
        theme_name = get_message('theme_light', language)
    elif query.data == "set_theme_dark":
        theme = "dark"
        theme_name = get_message('theme_dark', language)
    else:  # set_theme_auto
        theme = "auto"
        theme_name = get_message('theme_auto', language)

        # Try to get Telegram client theme
        try:
            # For auto theme, we'll try to detect if the user is using Telegram's dark mode
            # We can do this by checking the user's settings if available
            # Since Telegram doesn't provide direct API access to this, we'll use a better heuristic

            # First, check if we can get the user's settings
            user = update.effective_user

            # Try to detect based on user's activity pattern
            from datetime import datetime
            current_hour = datetime.now().hour

            # We'll use a more sophisticated approach:
            # 1. Check the time of day (people tend to use dark mode at night)
            # 2. Store the user's preference in the database for consistency

            # Default to dark theme during night hours (7 PM - 7 AM)
            if current_hour >= 19 or current_hour < 7:
                auto_theme = "dark"
            else:
                auto_theme = "light"

            # Store the auto-detected theme
            context.user_data['auto_theme'] = auto_theme

            # Also store this in the database for persistence
            db.set_user_auto_theme_preference(user_id, auto_theme)

            # Inform the user about auto theme
            if language == 'ru':
                query.answer(f"Тема будет автоматически синхронизироваться с Telegram")
            elif language == 'en':
                query.answer(f"Theme will automatically sync with Telegram")
            else:
                query.answer(f"Tema Telegram bilen awtomatiki sinhronizirlenýär")

        except Exception as e:
            print(f"Error detecting Telegram theme: {e}")

    # Update theme in database
    db.set_user_theme(user_id, theme)

    # Notify user
    query.answer(f"{get_message('theme_changed', language)}: {theme_name}")

    # Return to settings
    from handlers.settings import settings_callback
    settings_callback(update, context)

def get_effective_theme(user_id, context=None):
    """Get the effective theme for a user, considering auto theme setting"""
    db = Database()
    user_info = db.get_user_info(user_id)
    theme = user_info.get('theme', 'light')

    # If theme is auto, determine the actual theme to use
    if theme == 'auto':
        # First check if we have a stored auto theme preference in context
        if context and 'auto_theme' in context.user_data:
            return context.user_data['auto_theme']

        # Then check if we have a stored preference in the database
        # First check if auto_theme column exists
        db.cur.execute("PRAGMA table_info(users)")
        columns = db.cur.fetchall()
        column_names = [column[1] for column in columns]

        if 'auto_theme' in column_names:
            # Get the stored auto theme preference
            db.cur.execute("""
                SELECT auto_theme FROM users
                WHERE user_id = ?
            """, (user_id,))
            result = db.cur.fetchone()
            if result and result[0]:
                return result[0]

        # If no stored preference, use time-based logic
        from datetime import datetime
        current_hour = datetime.now().hour

        # Default to dark theme during night hours (7 PM - 7 AM)
        if current_hour >= 19 or current_hour < 7:
            return "dark"
        else:
            return "light"

    return theme
