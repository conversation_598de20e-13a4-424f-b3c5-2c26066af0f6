from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext
from utils.db import Database
from utils.languages import get_message
from utils.keyboards import get_history_keyboard, get_favorites_keyboard

def history_command(update: Update, context: CallbackContext):
    """Handle /history command"""
    user_id = update.effective_user.id
    
    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)
    
    # Get search history
    history = db.get_search_history(user_id)
    
    if not history:
        # No history found
        update.message.reply_text(
            get_message('no_history', language),
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton(get_message('search_button', language), callback_data="search_start")
            ]])
        )
        return
    
    # Format history message
    history_text = f"<b>{get_message('history_title', language)}</b>\n\n"
    
    for i, item in enumerate(history[:10], 1):  # Show only the last 10 searches
        query = item.get('query', 'N/A')
        timestamp = item.get('timestamp', 'N/A')
        history_text += f"{i}. <code>{query}</code> - {timestamp}\n"
    
    # Add note if there are more items
    if len(history) > 10:
        history_text += f"\n<i>+ {len(history) - 10} {get_message('more_items', language)}</i>"
    
    # Send history message
    update.message.reply_text(
        history_text,
        parse_mode="HTML",
        reply_markup=get_history_keyboard(language)
    )

def favorites_command(update: Update, context: CallbackContext):
    """Handle /favorites command"""
    user_id = update.effective_user.id
    
    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)
    
    # Get favorites
    favorites = db.get_favorites(user_id)
    
    if not favorites:
        # No favorites found
        update.message.reply_text(
            get_message('no_favorites', language),
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton(get_message('search_button', language), callback_data="search_start")
            ]])
        )
        return
    
    # Format favorites message
    favorites_text = f"<b>{get_message('favorites_title', language)}</b>\n\n"
    
    for i, item in enumerate(favorites[:5], 1):  # Show only the first 5 favorites
        name = item.get('name', 'N/A')
        phone = item.get('phone', 'N/A')
        favorites_text += f"{i}. <b>{name}</b>\n   📱 <code>{phone}</code>\n\n"
    
    # Add note if there are more items
    if len(favorites) > 5:
        favorites_text += f"\n<i>+ {len(favorites) - 5} {get_message('more_items', language)}</i>"
    
    # Create keyboard for navigation
    keyboard = [
        [InlineKeyboardButton("⬅️ 1-5", callback_data="prev_favorite_0"),
         InlineKeyboardButton("6-10 ➡️", callback_data="next_favorite_5")]
    ]
    
    # Add delete button and back button
    keyboard.append([InlineKeyboardButton(get_message('delete_favorite', language), callback_data="delete_favorite")])
    keyboard.append([InlineKeyboardButton(get_message('back', language), callback_data="main_menu")])
    
    # Send favorites message
    update.message.reply_text(
        favorites_text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )
