from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext
from utils.db import Database
from utils.keyboards import get_main_menu_keyboard, get_language_keyboard
from utils.languages import get_message

def language_callback(update: Update, context: CallbackContext):
    """Handle language selection callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get the selected language from callback data
    selected_lang = query.data.split('_')[1]

    # Update user's language preference in database
    db = Database()
    db.set_user_language(user_id, selected_lang)

    # Get welcome message in selected language
    welcome_text = get_message('welcome', selected_lang)

    # Show main menu with buttons in selected language
    query.edit_message_text(
        welcome_text,
        reply_markup=get_main_menu_keyboard(selected_lang, user_id)
    )

def change_language_callback(update: Update, context: CallbackContext):
    """Handle language change request"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's current language
    db = Database()
    language = db.get_user_language(user_id)

    # Show language selection menu
    query.edit_message_text(
        get_message('select_new_language', language),
        reply_markup=get_language_keyboard()
    )