"""
Search history analysis module for analyzing user search patterns
"""
import logging
from utils.db import Database
import datetime
import json
import os
from collections import Counter

# Set up logging
logging.basicConfig(format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_search_history(user_id=None, days=30):
    """
    Analyze search history for a specific user or all users
    
    Args:
        user_id: Optional user ID to filter results for a specific user
        days: Number of days to look back
        
    Returns:
        Dictionary with analysis results
    """
    try:
        db = Database()
        
        # Calculate date range
        end_date = datetime.datetime.now()
        start_date = end_date - datetime.timedelta(days=days)
        
        # Format dates for SQL query
        start_date_str = start_date.strftime('%Y-%m-%d %H:%M:%S')
        end_date_str = end_date.strftime('%Y-%m-%d %H:%M:%S')
        
        # Get search history
        if user_id:
            db.cur.execute("""
                SELECT query, search_type, search_date
                FROM searches
                WHERE user_id = ? AND search_date BETWEEN ? AND ?
                ORDER BY search_date DESC
            """, (user_id, start_date_str, end_date_str))
        else:
            db.cur.execute("""
                SELECT query, search_type, search_date, user_id
                FROM searches
                WHERE search_date BETWEEN ? AND ?
                ORDER BY search_date DESC
            """, (start_date_str, end_date_str))
        
        searches = db.cur.fetchall()
        
        # Prepare results
        results = {
            'total_searches': len(searches),
            'date_range': {
                'start': start_date_str,
                'end': end_date_str
            }
        }
        
        # If no searches found, return early
        if not searches:
            results['most_common_queries'] = []
            results['search_types'] = {}
            results['search_by_day'] = {}
            results['search_by_hour'] = {}
            return results
        
        # Analyze search patterns
        queries = []
        search_types = Counter()
        search_by_day = Counter()
        search_by_hour = Counter()
        
        for search in searches:
            if user_id:
                query, search_type, search_date = search
            else:
                query, search_type, search_date, _ = search
            
            # Add query to list
            queries.append(query)
            
            # Count search types
            search_types[search_type] += 1
            
            # Parse search date
            try:
                date_obj = datetime.datetime.strptime(search_date, '%Y-%m-%d %H:%M:%S')
                
                # Count searches by day of week
                day_name = date_obj.strftime('%A')
                search_by_day[day_name] += 1
                
                # Count searches by hour
                hour = date_obj.hour
                search_by_hour[hour] += 1
            except:
                pass
        
        # Get most common queries
        most_common_queries = Counter(queries).most_common(10)
        
        # Add results to dictionary
        results['most_common_queries'] = [{'query': q, 'count': c} for q, c in most_common_queries]
        results['search_types'] = dict(search_types)
        results['search_by_day'] = dict(search_by_day)
        results['search_by_hour'] = {str(h): c for h, c in search_by_hour.items()}
        
        return results
    
    except Exception as e:
        logger.error(f"Error analyzing search history: {e}")
        return {'error': str(e)}

def get_user_recommendations(user_id, limit=5):
    """
    Get search recommendations for a user based on their search history
    
    Args:
        user_id: User ID to get recommendations for
        limit: Maximum number of recommendations to return
        
    Returns:
        List of recommended search queries
    """
    try:
        # Get user's search history
        analysis = analyze_search_history(user_id, days=90)
        
        # If no searches or error, return empty list
        if 'error' in analysis or analysis['total_searches'] == 0:
            return []
        
        # Get most common queries
        recommendations = [item['query'] for item in analysis['most_common_queries'][:limit]]
        
        return recommendations
    
    except Exception as e:
        logger.error(f"Error getting user recommendations: {e}")
        return []

def generate_search_report(user_id=None, days=30):
    """
    Generate a report of search history for a user or all users
    
    Args:
        user_id: Optional user ID to filter results for a specific user
        days: Number of days to look back
        
    Returns:
        Report text in markdown format
    """
    try:
        # Get analysis results
        analysis = analyze_search_history(user_id, days)
        
        # If error, return error message
        if 'error' in analysis:
            return f"Error generating report: {analysis['error']}"
        
        # Generate report
        report = []
        report.append(f"# Gözleg taryhy hasabaty")
        report.append(f"Döwür: {analysis['date_range']['start']} - {analysis['date_range']['end']}")
        report.append(f"Jemi gözlegler: {analysis['total_searches']}")
        
        # Add most common queries
        report.append("\n## Iň köp gözlenen sözler")
        if analysis['most_common_queries']:
            for i, item in enumerate(analysis['most_common_queries'], 1):
                report.append(f"{i}. {item['query']} - {item['count']} gezek")
        else:
            report.append("Gözleg tapylmady")
        
        # Add search types
        report.append("\n## Gözleg görnüşleri")
        for search_type, count in analysis['search_types'].items():
            report.append(f"- {search_type}: {count} gezek")
        
        # Add search by day
        report.append("\n## Günler boýunça gözlegler")
        for day, count in analysis['search_by_day'].items():
            report.append(f"- {day}: {count} gezek")
        
        # Add search by hour
        report.append("\n## Sagatlar boýunça gözlegler")
        for hour, count in sorted(analysis['search_by_hour'].items(), key=lambda x: int(x[0])):
            report.append(f"- {hour}:00: {count} gezek")
        
        return "\n".join(report)
    
    except Exception as e:
        logger.error(f"Error generating search report: {e}")
        return f"Error generating report: {e}"

def save_analysis_to_file(user_id=None, days=30, filename=None):
    """
    Save search history analysis to a file
    
    Args:
        user_id: Optional user ID to filter results for a specific user
        days: Number of days to look back
        filename: Optional filename to save to
        
    Returns:
        Path to saved file or None if error
    """
    try:
        # Get analysis results
        analysis = analyze_search_history(user_id, days)
        
        # If error, return None
        if 'error' in analysis:
            return None
        
        # Create directory if it doesn't exist
        os.makedirs('stats/search_analysis', exist_ok=True)
        
        # Generate filename if not provided
        if not filename:
            current_date = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            if user_id:
                filename = f"stats/search_analysis/user_{user_id}_{current_date}.json"
            else:
                filename = f"stats/search_analysis/all_users_{current_date}.json"
        
        # Save to file
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, ensure_ascii=False, indent=2)
        
        return filename
    
    except Exception as e:
        logger.error(f"Error saving analysis to file: {e}")
        return None