from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext
from utils.db import Database
import datetime
import matplotlib.pyplot as plt
import io
import os

def admin_stats_command(update: Update, context: CallbackContext):
    """Handle the /admin_stats command"""
    user_id = update.effective_user.id
    
    # Check if user is admin
    from config import ADMIN_IDS
    if user_id not in ADMIN_IDS:
        update.message.reply_text("Bu komanda diňe administratorlar üçin elýeterlidir.")
        return
    
    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)
    
    # Create keyboard with stats options
    keyboard = [
        [InlineKeyboardButton("Ulanyjylar statistikasy", callback_data="admin_stats_users")],
        [InlineKeyboardButton("Gözlegler statistikasy", callback_data="admin_stats_searches")],
        [InlineKeyboardButton("VIP statistikasy", callback_data="admin_stats_vip")],
        [InlineKeyboardButton("Grafik görkezmek", callback_data="admin_stats_graph")],
        [InlineKeyboardButton("Yza", callback_data="admin_panel")]
    ]
    
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    # Send message with stats options
    if language == 'ru':
        message = "📊 <b>Панель статистики администратора</b>\n\nВыберите тип статистики для просмотра:"
    elif language == 'en':
        message = "📊 <b>Admin Statistics Panel</b>\n\nSelect the type of statistics to view:"
    else:
        message = "📊 <b>Administrator statistika paneli</b>\n\nGörmek isleýän statistika görnüşiňizi saýlaň:"
    
    update.message.reply_text(message, reply_markup=reply_markup, parse_mode='HTML')

def admin_stats_callback(update: Update, context: CallbackContext):
    """Handle admin stats callbacks"""
    query = update.callback_query
    user_id = query.from_user.id
    callback_data = query.data
    
    # Check if user is admin
    from config import ADMIN_IDS
    if user_id not in ADMIN_IDS:
        query.answer("Bu funksiýa diňe administratorlar üçin elýeterlidir.")
        return
    
    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)
    
    # Handle different stats options
    if callback_data == "admin_stats_users":
        show_users_stats(query, db, language)
    elif callback_data == "admin_stats_searches":
        show_searches_stats(query, db, language)
    elif callback_data == "admin_stats_vip":
        show_vip_stats(query, db, language)
    elif callback_data == "admin_stats_graph":
        show_stats_graph(query, db, language)
    elif callback_data == "admin_stats_back":
        # Return to main stats menu
        keyboard = [
            [InlineKeyboardButton("Ulanyjylar statistikasy", callback_data="admin_stats_users")],
            [InlineKeyboardButton("Gözlegler statistikasy", callback_data="admin_stats_searches")],
            [InlineKeyboardButton("VIP statistikasy", callback_data="admin_stats_vip")],
            [InlineKeyboardButton("Grafik görkezmek", callback_data="admin_stats_graph")],
            [InlineKeyboardButton("Yza", callback_data="admin_panel")]
        ]
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        if language == 'ru':
            message = "📊 <b>Панель статистики администратора</b>\n\nВыберите тип статистики для просмотра:"
        elif language == 'en':
            message = "📊 <b>Admin Statistics Panel</b>\n\nSelect the type of statistics to view:"
        else:
            message = "📊 <b>Administrator statistika paneli</b>\n\nGörmek isleýän statistika görnüşiňizi saýlaň:"
        
        query.edit_message_text(message, reply_markup=reply_markup, parse_mode='HTML')

def show_users_stats(query, db, language):
    """Show user statistics"""
    # Get user stats
    total_users = db.get_total_users_count()
    vip_users = db.get_vip_users_count()
    
    # Get new users counts
    new_today = len(db.get_new_users(days=1))
    new_week = len(db.get_new_users(days=7))
    new_month = len(db.get_new_users(days=30))
    
    # Get top users by points
    top_users = db.get_top_users_by_points(limit=5)
    top_users_text = ""
    for i, user in enumerate(top_users, 1):
        top_users_text += f"{i}. {user['full_name']} ({user['username']}) - {user['points']} bal\n"
    
    # Get top referrers
    top_referrers = db.get_top_users_by_referrals(limit=5)
    top_referrers_text = ""
    for i, user in enumerate(top_referrers, 1):
        top_referrers_text += f"{i}. {user['full_name']} ({user['username']}) - {user['referrals']} ulanyjy\n"
    
    # Create message based on language
    if language == 'ru':
        message = f"👥 <b>Статистика пользователей</b>\n\n"
        message += f"📊 Всего пользователей: {total_users}\n"
        message += f"⭐ VIP пользователей: {vip_users} ({vip_users/total_users*100:.1f}%)\n\n"
        message += f"📈 <b>Новые пользователи:</b>\n"
        message += f"• Сегодня: {new_today}\n"
        message += f"• За неделю: {new_week}\n"
        message += f"• За месяц: {new_month}\n\n"
        message += f"🏆 <b>Топ пользователей по баллам:</b>\n{top_users_text}\n"
        message += f"👥 <b>Топ по рефералам:</b>\n{top_referrers_text}"
    elif language == 'en':
        message = f"👥 <b>User Statistics</b>\n\n"
        message += f"📊 Total users: {total_users}\n"
        message += f"⭐ VIP users: {vip_users} ({vip_users/total_users*100:.1f}%)\n\n"
        message += f"📈 <b>New users:</b>\n"
        message += f"• Today: {new_today}\n"
        message += f"• This week: {new_week}\n"
        message += f"• This month: {new_month}\n\n"
        message += f"🏆 <b>Top users by points:</b>\n{top_users_text}\n"
        message += f"👥 <b>Top referrers:</b>\n{top_referrers_text}"
    else:
        message = f"👥 <b>Ulanyjy statistikasy</b>\n\n"
        message += f"📊 Jemi ulanyjylar: {total_users}\n"
        message += f"⭐ VIP ulanyjylar: {vip_users} ({vip_users/total_users*100:.1f}%)\n\n"
        message += f"📈 <b>Täze ulanyjylar:</b>\n"
        message += f"• Şu gün: {new_today}\n"
        message += f"• Şu hepde: {new_week}\n"
        message += f"• Şu aý: {new_month}\n\n"
        message += f"🏆 <b>Iň köp bally ulanyjylar:</b>\n{top_users_text}\n"
        message += f"👥 <b>Iň köp çagyran ulanyjylar:</b>\n{top_referrers_text}"
    
    # Create back button
    keyboard = [[InlineKeyboardButton("Yza", callback_data="admin_stats_back")]]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    # Send message
    query.edit_message_text(message, reply_markup=reply_markup, parse_mode='HTML')

def show_searches_stats(query, db, language):
    """Show search statistics"""
    # Get search stats
    daily_searches = db.get_daily_searches_count()
    weekly_searches = db.get_weekly_searches_count()
    monthly_searches = db.get_monthly_searches_count()
    
    # Get most searched queries
    most_searched = db.get_most_searched_queries(limit=10)
    most_searched_text = ""
    for i, (query_text, count) in enumerate(most_searched, 1):
        most_searched_text += f"{i}. {query_text} - {count} gezek\n"
    
    # Create message based on language
    if language == 'ru':
        message = f"🔍 <b>Статистика поисков</b>\n\n"
        message += f"📊 <b>Количество поисков:</b>\n"
        message += f"• Сегодня: {daily_searches}\n"
        message += f"• За неделю: {weekly_searches}\n"
        message += f"• За месяц: {monthly_searches}\n\n"
        message += f"🔝 <b>Самые популярные запросы:</b>\n{most_searched_text}"
    elif language == 'en':
        message = f"🔍 <b>Search Statistics</b>\n\n"
        message += f"📊 <b>Number of searches:</b>\n"
        message += f"• Today: {daily_searches}\n"
        message += f"• This week: {weekly_searches}\n"
        message += f"• This month: {monthly_searches}\n\n"
        message += f"🔝 <b>Most popular queries:</b>\n{most_searched_text}"
    else:
        message = f"🔍 <b>Gözleg statistikasy</b>\n\n"
        message += f"📊 <b>Gözlegleriň sany:</b>\n"
        message += f"• Şu gün: {daily_searches}\n"
        message += f"• Şu hepde: {weekly_searches}\n"
        message += f"• Şu aý: {monthly_searches}\n\n"
        message += f"🔝 <b>Iň köp gözlenen sözler:</b>\n{most_searched_text}"
    
    # Create back button
    keyboard = [[InlineKeyboardButton("Yza", callback_data="admin_stats_back")]]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    # Send message
    query.edit_message_text(message, reply_markup=reply_markup, parse_mode='HTML')

def show_vip_stats(query, db, language):
    """Show VIP statistics"""
    # Get VIP stats
    total_users = db.get_total_users_count()
    vip_users = db.get_vip_users_count()
    vip_percentage = (vip_users / total_users * 100) if total_users > 0 else 0
    
    # Create message based on language
    if language == 'ru':
        message = f"⭐ <b>Статистика VIP</b>\n\n"
        message += f"👥 Всего пользователей: {total_users}\n"
        message += f"⭐ VIP пользователей: {vip_users}\n"
        message += f"📊 Процент VIP: {vip_percentage:.1f}%\n\n"
    elif language == 'en':
        message = f"⭐ <b>VIP Statistics</b>\n\n"
        message += f"👥 Total users: {total_users}\n"
        message += f"⭐ VIP users: {vip_users}\n"
        message += f"📊 VIP percentage: {vip_percentage:.1f}%\n\n"
    else:
        message = f"⭐ <b>VIP statistikasy</b>\n\n"
        message += f"👥 Jemi ulanyjylar: {total_users}\n"
        message += f"⭐ VIP ulanyjylar: {vip_users}\n"
        message += f"📊 VIP göterimi: {vip_percentage:.1f}%\n\n"
    
    # Create back button
    keyboard = [[InlineKeyboardButton("Yza", callback_data="admin_stats_back")]]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    # Send message
    query.edit_message_text(message, reply_markup=reply_markup, parse_mode='HTML')

def show_stats_graph(query, db, language):
    """Show statistics graph"""
    try:
        # Create a directory for temporary files if it doesn't exist
        if not os.path.exists('temp'):
            os.makedirs('temp')
        
        # Create a graph of searches over time
        plt.figure(figsize=(10, 6))
        
        # Get data for the last 7 days
        dates = []
        search_counts = []
        
        for i in range(7, 0, -1):
            date = datetime.datetime.now() - datetime.timedelta(days=i)
            date_str = date.strftime('%Y-%m-%d')
            
            # Get searches for this date
            db.cur.execute("""
                SELECT COUNT(*) FROM searches
                WHERE DATE(search_time) = ?
            """, (date_str,))
            count = db.cur.fetchone()[0]
            
            dates.append(date.strftime('%d-%m'))
            search_counts.append(count)
        
        # Plot the data
        plt.plot(dates, search_counts, marker='o', linestyle='-', color='blue')
        plt.title('Soňky 7 günde gözlegler')
        plt.xlabel('Sene')
        plt.ylabel('Gözlegleriň sany')
        plt.grid(True)
        
        # Save the plot to a temporary file
        graph_path = 'temp/search_graph.png'
        plt.savefig(graph_path)
        plt.close()
        
        # Send the graph
        with open(graph_path, 'rb') as photo:
            query.message.reply_photo(
                photo=photo,
                caption="📊 Soňky 7 günde gözlegleriň statistikasy"
            )
        
        # Send a message to go back
        keyboard = [[InlineKeyboardButton("Yza", callback_data="admin_stats_back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        if language == 'ru':
            message = "📊 <b>График статистики</b>\n\nВыше показан график поисков за последние 7 дней."
        elif language == 'en':
            message = "📊 <b>Statistics Graph</b>\n\nAbove is a graph of searches for the last 7 days."
        else:
            message = "📊 <b>Statistika grafigi</b>\n\nÝokarda soňky 7 günde gözlegleriň grafigi görkezilýär."
        
        query.edit_message_text(message, reply_markup=reply_markup, parse_mode='HTML')
        
    except Exception as e:
        print(f"Error creating graph: {e}")
        
        # Send error message
        keyboard = [[InlineKeyboardButton("Yza", callback_data="admin_stats_back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        if language == 'ru':
            message = "❌ <b>Ошибка</b>\n\nНе удалось создать график статистики."
        elif language == 'en':
            message = "❌ <b>Error</b>\n\nFailed to create statistics graph."
        else:
            message = "❌ <b>Ýalňyşlyk</b>\n\nStatistika grafigini döredip bolmady."
        
        query.edit_message_text(message, reply_markup=reply_markup, parse_mode='HTML')