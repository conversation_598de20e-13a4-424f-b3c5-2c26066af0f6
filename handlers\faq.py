from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext
from utils.db import Database
from utils.keyboards import get_back_button
from utils.languages import get_message

def faq_callback(update: Update, context: CallbackContext):
    """Handle FAQ callback"""
    query = update.callback_query
    user_id = query.from_user.id
    
    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)
    
    # Show FAQ content
    query.edit_message_text(
        get_message('faq_title', language) + "\n\n" + get_message('faq_content', language),
        reply_markup=get_back_button("main_menu", language)
    )
