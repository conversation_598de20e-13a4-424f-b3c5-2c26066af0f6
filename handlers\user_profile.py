from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext, ConversationHandler
from utils.constants import ParseMode
from utils.db import Database
from utils.keyboards import get_back_button
from utils.languages import get_message

# Conversation states
PROFILE_MENU, EDIT_NAME, EDIT_EMAIL, EDIT_PHONE, EDIT_BIO, UPLOAD_PHOTO = range(6)
SEARCH_PREFS, SET_RESULTS_PER_PAGE, SET_SORT_ORDER, SET_DEFAULT_TYPE = range(6, 10)

def user_profile_command(update: Update, context: CallbackContext):
    """Handle /profile command to view and edit user profile"""
    user_id = update.effective_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get user info
    user_info = db.get_user_info(user_id)

    # Format profile text
    profile_text = format_profile_text(user_info, language)

    # Create keyboard - removed settings button
    keyboard = [
        [InlineKeyboardButton(get_message('edit_profile', language), callback_data="edit_profile")],
        [InlineKeyboardButton(get_message('back', language), callback_data="main_menu")]
    ]

    # Send profile info
    update.message.reply_text(
        profile_text,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

    return PROFILE_MENU

def profile_callback(update: Update, context: CallbackContext):
    """Handle profile callback queries"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get user info
    user_info = db.get_user_info(user_id)

    # Format profile text
    profile_text = format_profile_text(user_info, language)

    # Create keyboard - removed settings button
    keyboard = [
        [InlineKeyboardButton(get_message('edit_profile', language), callback_data="edit_profile")],
        [InlineKeyboardButton(get_message('back', language), callback_data="main_menu")]
    ]

    # Edit message with profile info
    query.edit_message_text(
        profile_text,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

    return PROFILE_MENU

def edit_profile_callback(update: Update, context: CallbackContext):
    """Handle edit profile callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Create keyboard - restore original options
    keyboard = [
        [InlineKeyboardButton(get_message('edit_name', language), callback_data="edit_name")],
        [InlineKeyboardButton(get_message('edit_email', language), callback_data="edit_email")],
        [InlineKeyboardButton(get_message('edit_phone', language), callback_data="edit_phone")],
        [InlineKeyboardButton(get_message('edit_bio', language), callback_data="edit_bio")],
        [InlineKeyboardButton(get_message('upload_photo', language), callback_data="upload_photo")],
        [InlineKeyboardButton(get_message('back', language), callback_data="profile_view")]
    ]

    # Edit message with edit options
    query.edit_message_text(
        get_message('edit_profile_text', language),
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

    return PROFILE_MENU

def edit_name_callback(update: Update, context: CallbackContext):
    """Handle edit name callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Ask for new name
    query.edit_message_text(
        get_message('enter_new_name', language),
        reply_markup=get_back_button("edit_profile", language)
    )

    return EDIT_NAME

def edit_email_callback(update: Update, context: CallbackContext):
    """Handle edit email callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Ask for new email
    query.edit_message_text(
        get_message('enter_new_email', language),
        reply_markup=get_back_button("edit_profile", language)
    )

    return EDIT_EMAIL

def edit_phone_callback(update: Update, context: CallbackContext):
    """Handle edit phone callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Ask for new phone
    query.edit_message_text(
        get_message('enter_new_phone', language),
        reply_markup=get_back_button("edit_profile", language)
    )

    return EDIT_PHONE

def edit_bio_callback(update: Update, context: CallbackContext):
    """Handle edit bio callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Ask for new bio
    query.edit_message_text(
        get_message('enter_new_bio', language),
        reply_markup=get_back_button("edit_profile", language)
    )

    return EDIT_BIO

def upload_photo_callback(update: Update, context: CallbackContext):
    """Handle upload photo callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Ask for new photo
    query.edit_message_text(
        get_message('upload_new_photo', language),
        reply_markup=get_back_button("edit_profile", language)
    )

    return UPLOAD_PHOTO

def handle_name_input(update: Update, context: CallbackContext):
    """Handle name input"""
    user_id = update.effective_user.id
    new_name = update.message.text

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Update name in database
    db.update_user_profile(user_id, full_name=new_name)

    # Send confirmation
    update.message.reply_text(
        get_message('name_updated', language),
        parse_mode=ParseMode.HTML
    )

    # Return to profile view
    return profile_callback(update, context)

def handle_email_input(update: Update, context: CallbackContext):
    """Handle email input"""
    user_id = update.effective_user.id
    new_email = update.message.text

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Simple email validation
    if '@' not in new_email or '.' not in new_email:
        update.message.reply_text(
            get_message('invalid_email', language),
            parse_mode=ParseMode.HTML
        )
        return EDIT_EMAIL

    # Update email in database
    db.update_user_profile(user_id, email=new_email)

    # Send confirmation
    update.message.reply_text(
        get_message('email_updated', language),
        parse_mode=ParseMode.HTML
    )

    # Return to profile view
    return profile_callback(update, context)

def handle_phone_input(update: Update, context: CallbackContext):
    """Handle phone input"""
    user_id = update.effective_user.id
    new_phone = update.message.text

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Simple phone validation
    if not new_phone.isdigit() or len(new_phone) < 8:
        update.message.reply_text(
            get_message('invalid_phone', language),
            parse_mode=ParseMode.HTML
        )
        return EDIT_PHONE

    # Update phone in database
    db.update_user_profile(user_id, phone=new_phone)

    # Send confirmation
    update.message.reply_text(
        get_message('phone_updated', language),
        parse_mode=ParseMode.HTML
    )

    # Return to profile view
    return profile_callback(update, context)

def handle_bio_input(update: Update, context: CallbackContext):
    """Handle bio input"""
    user_id = update.effective_user.id
    new_bio = update.message.text

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Update bio in database
    db.update_user_profile(user_id, bio=new_bio)

    # Send confirmation
    update.message.reply_text(
        get_message('bio_updated', language),
        parse_mode=ParseMode.HTML
    )

    # Return to profile view
    return profile_callback(update, context)

def handle_photo_upload(update: Update, context: CallbackContext):
    """Handle photo upload"""
    user_id = update.effective_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Check if message contains photo
    if not update.message.photo:
        update.message.reply_text(
            get_message('no_photo_found', language),
            parse_mode=ParseMode.HTML
        )
        return UPLOAD_PHOTO

    # Get photo file ID (largest size)
    photo_file_id = update.message.photo[-1].file_id

    # Update photo in database
    db.update_user_profile(user_id, profile_photo=photo_file_id)

    # Send confirmation
    update.message.reply_text(
        get_message('photo_updated', language),
        parse_mode=ParseMode.HTML
    )

    # Return to profile view
    return profile_callback(update, context)

def search_preferences_callback(update: Update, context: CallbackContext):
    """Handle search preferences callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get user's search preferences
    preferences = db.get_search_preferences(user_id)

    # Format preferences text
    preferences_text = format_preferences_text(preferences, language)

    # Create keyboard
    keyboard = [
        [InlineKeyboardButton(get_message('set_results_per_page', language), callback_data="set_results_per_page")],
        [InlineKeyboardButton(get_message('set_sort_order', language), callback_data="set_sort_order")],
        [InlineKeyboardButton(get_message('set_default_type', language), callback_data="set_default_type")],
        [InlineKeyboardButton(get_message('back', language), callback_data="profile_view")]
    ]

    # Edit message with preferences info
    query.edit_message_text(
        preferences_text,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

    return SEARCH_PREFS

def set_results_per_page_callback(update: Update, context: CallbackContext):
    """Handle set results per page callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Create keyboard with options
    keyboard = [
        [InlineKeyboardButton("3", callback_data="results_per_page_3"),
         InlineKeyboardButton("5", callback_data="results_per_page_5"),
         InlineKeyboardButton("10", callback_data="results_per_page_10")],
        [InlineKeyboardButton(get_message('back', language), callback_data="search_preferences")]
    ]

    # Edit message with options
    query.edit_message_text(
        get_message('select_results_per_page', language),
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

    return SEARCH_PREFS

def set_sort_order_callback(update: Update, context: CallbackContext):
    """Handle set sort order callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Create keyboard with options
    keyboard = [
        [InlineKeyboardButton(get_message('sort_newest', language), callback_data="sort_order_desc")],
        [InlineKeyboardButton(get_message('sort_oldest', language), callback_data="sort_order_asc")],
        [InlineKeyboardButton(get_message('back', language), callback_data="search_preferences")]
    ]

    # Edit message with options
    query.edit_message_text(
        get_message('select_sort_order', language),
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

    return SEARCH_PREFS

def set_default_type_callback(update: Update, context: CallbackContext):
    """Handle set default search type callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Create keyboard with options
    keyboard = [
        [InlineKeyboardButton(get_message('search_type_phone', language), callback_data="default_type_phone")],
        [InlineKeyboardButton(get_message('search_type_name', language), callback_data="default_type_name")],
        [InlineKeyboardButton(get_message('search_type_passport', language), callback_data="default_type_passport")],
        [InlineKeyboardButton(get_message('back', language), callback_data="search_preferences")]
    ]

    # Edit message with options
    query.edit_message_text(
        get_message('select_default_type', language),
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

    return SEARCH_PREFS

def handle_results_per_page(update: Update, context: CallbackContext):
    """Handle results per page selection"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get selected value
    results_per_page = int(query.data.split('_')[-1])

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Save preference
    db.save_search_preferences(user_id, results_per_page=results_per_page)

    # Inform user
    query.answer(get_message('results_per_page_updated', language))

    # Return to search preferences
    return search_preferences_callback(update, context)

def handle_sort_order(update: Update, context: CallbackContext):
    """Handle sort order selection"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get selected value
    sort_order = query.data.split('_')[-1]

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Save preference
    db.save_search_preferences(user_id, sort_order=sort_order)

    # Inform user
    query.answer(get_message('sort_order_updated', language))

    # Return to search preferences
    return search_preferences_callback(update, context)

def handle_default_type(update: Update, context: CallbackContext):
    """Handle default search type selection"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get selected value
    default_type = query.data.split('_')[-1]

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Save preference
    db.save_search_preferences(user_id, default_search_type=default_type)

    # Inform user
    query.answer(get_message('default_type_updated', language))

    # Return to search preferences
    return search_preferences_callback(update, context)

def format_profile_text(user_info, language):
    """Format user profile text"""
    db = Database()

    # Get additional user info
    try:
        # Get email, bio, etc.
        db.cur.execute("""
            SELECT email, bio, profile_photo
            FROM users
            WHERE user_id = ?
        """, (user_info['user_id'],))

        result = db.cur.fetchone()

        if result:
            email = result[0] if result[0] else get_message('not_set', language)
            bio = result[1] if result[1] else get_message('not_set', language)
            has_photo = bool(result[2])
        else:
            email = get_message('not_set', language)
            bio = get_message('not_set', language)
            has_photo = False
    except Exception as e:
        print(f"Error getting additional user info: {e}")
        email = get_message('not_set', language)
        bio = get_message('not_set', language)
        has_photo = False

    # Format text based on language
    if language == 'ru':
        profile_text = (
            f"<b>👤 Профиль пользователя</b>\n\n"
            f"🆔 ID: <code>{user_info['user_id']}</code>\n"
            f"👤 Имя: <code>{user_info['full_name']}</code>\n"
            f"📱 Телефон: <code>{user_info.get('phone_number', get_message('not_set', language))}</code>\n"
            f"📧 Email: <code>{email}</code>\n"
            f"📝 О себе: <code>{bio}</code>\n"
            f"🖼 Фото: {'✅ Установлено' if has_photo else '❌ Не установлено'}\n\n"
            f"🔍 Всего поисков: <code>{user_info['total_searches']}</code>\n"
            f"⭐ VIP статус: {'✅ Активен' if user_info['is_vip'] else '❌ Неактивен'}\n"
            f"👥 Приглашено пользователей: <code>{db.get_referral_count(user_info['user_id'])}</code>"
        )
    elif language == 'en':
        profile_text = (
            f"<b>👤 User Profile</b>\n\n"
            f"🆔 ID: <code>{user_info['user_id']}</code>\n"
            f"👤 Name: <code>{user_info['full_name']}</code>\n"
            f"📱 Phone: <code>{user_info.get('phone_number', get_message('not_set', language))}</code>\n"
            f"📧 Email: <code>{email}</code>\n"
            f"📝 Bio: <code>{bio}</code>\n"
            f"🖼 Photo: {'✅ Set' if has_photo else '❌ Not set'}\n\n"
            f"🔍 Total searches: <code>{user_info['total_searches']}</code>\n"
            f"⭐ VIP status: {'✅ Active' if user_info['is_vip'] else '❌ Inactive'}\n"
            f"👥 Referred users: <code>{db.get_referral_count(user_info['user_id'])}</code>"
        )
    else:  # Default to Turkmen
        profile_text = (
            f"<b>👤 Ulanyjy profili</b>\n\n"
            f"🆔 ID: <code>{user_info['user_id']}</code>\n"
            f"👤 Ady: <code>{user_info['full_name']}</code>\n"
            f"📱 Telefon: <code>{user_info.get('phone_number', get_message('not_set', language))}</code>\n"
            f"📧 Email: <code>{email}</code>\n"
            f"📝 Özüm barada: <code>{bio}</code>\n"
            f"🖼 Surat: {'✅ Goýuldy' if has_photo else '❌ Goýulmady'}\n\n"
            f"🔍 Jemi gözlegler: <code>{user_info['total_searches']}</code>\n"
            f"⭐ VIP ýagdaýy: {'✅ Işjeň' if user_info['is_vip'] else '❌ Işjeň däl'}\n"
            f"👥 Çagyrylan ulanyjylar: <code>{db.get_referral_count(user_info['user_id'])}</code>"
        )

    return profile_text

def format_preferences_text(preferences, language):
    """Format search preferences text"""
    # Format text based on language
    if language == 'ru':
        # Get sort order text
        if preferences['sort_order'] == 'desc':
            sort_order_text = "Сначала новые"
        else:
            sort_order_text = "Сначала старые"

        # Get default search type text
        if preferences['default_search_type'] == 'phone':
            default_type_text = "Телефон"
        elif preferences['default_search_type'] == 'name':
            default_type_text = "Имя"
        else:
            default_type_text = "Паспорт"

        preferences_text = (
            f"<b>🔍 Настройки поиска</b>\n\n"
            f"📊 Результатов на странице: <code>{preferences['results_per_page']}</code>\n"
            f"🔄 Порядок сортировки: <code>{sort_order_text}</code>\n"
            f"🔎 Тип поиска по умолчанию: <code>{default_type_text}</code>"
        )
    elif language == 'en':
        # Get sort order text
        if preferences['sort_order'] == 'desc':
            sort_order_text = "Newest first"
        else:
            sort_order_text = "Oldest first"

        # Get default search type text
        if preferences['default_search_type'] == 'phone':
            default_type_text = "Phone"
        elif preferences['default_search_type'] == 'name':
            default_type_text = "Name"
        else:
            default_type_text = "Passport"

        preferences_text = (
            f"<b>🔍 Search Preferences</b>\n\n"
            f"📊 Results per page: <code>{preferences['results_per_page']}</code>\n"
            f"🔄 Sort order: <code>{sort_order_text}</code>\n"
            f"🔎 Default search type: <code>{default_type_text}</code>"
        )
    else:  # Default to Turkmen
        # Get sort order text
        if preferences['sort_order'] == 'desc':
            sort_order_text = "Ilki täzeler"
        else:
            sort_order_text = "Ilki köneler"

        # Get default search type text
        if preferences['default_search_type'] == 'phone':
            default_type_text = "Telefon"
        elif preferences['default_search_type'] == 'name':
            default_type_text = "Ady"
        else:
            default_type_text = "Pasport"

        preferences_text = (
            f"<b>🔍 Gözleg sazlamalary</b>\n\n"
            f"📊 Sahypada netije sany: <code>{preferences['results_per_page']}</code>\n"
            f"🔄 Tertip: <code>{sort_order_text}</code>\n"
            f"🔎 Adaty gözleg görnüşi: <code>{default_type_text}</code>"
        )

    return preferences_text
