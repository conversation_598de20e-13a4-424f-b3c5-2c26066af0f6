from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from utils.constants import ParseMode
from telegram.ext import CallbackContext
from utils.db import Database
from utils.keyboards import get_back_button, get_theme_keyboard
from utils.languages import get_message

def settings_callback(update: Update, context: CallbackContext):
    """Handle settings view callback"""
    try:
        query = update.callback_query
        user_id = query.from_user.id

        # Get user data from database
        db = Database()
        language = db.get_user_language(user_id)
        user_data = db.get_user_info(user_id)

        if not user_data:
            query.edit_message_text(
                "Settings not found. Please try again.",
                reply_markup=get_back_button("main_menu", language)
            )
            return

        # Get current theme
        current_theme = user_data.get('theme', 'light')
        theme_text = get_message('theme_light', language) if current_theme == 'light' else get_message('theme_dark', language)

        # Format settings text
        settings_text = f"<b>⚙️ {get_message('settings_title', language)}</b>\n\n"
        settings_text += f"🌐 {get_message('language_setting', language)}: {get_language_name(language)}\n"
        settings_text += f"🌙 {get_message('theme_setting', language)}: {theme_text}\n"

        # Create keyboard with profile button
        keyboard = [
            [InlineKeyboardButton(get_message('profile_button', language), callback_data="profile_view")],
            [InlineKeyboardButton(get_message('change_language', language), callback_data="change_language")],
            [InlineKeyboardButton(get_message('theme_button', language), callback_data="change_theme")],
            [InlineKeyboardButton(get_message('notification_settings', language), callback_data="notification_settings")],
            [InlineKeyboardButton(get_message('edit_profile', language), callback_data="edit_profile")],
            [InlineKeyboardButton(get_message('search_preferences', language), callback_data="search_preferences")],
            [InlineKeyboardButton(get_message('back', language), callback_data="main_menu")]
        ]

        # Send settings info
        query.edit_message_text(
            settings_text,
            parse_mode=ParseMode.HTML,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
    except Exception as e:
        print(f"Error in settings_callback: {e}")
        query.edit_message_text(
            "Error loading settings. Please try again later.",
            reply_markup=get_back_button("main_menu", "tm")
        )

def notification_settings_callback(update: Update, context: CallbackContext):
    """Handle notification settings callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get current notification settings
    user_data = db.get_user_info(user_id)
    notifications_enabled = user_data.get('notifications_enabled', True)

    # Format notification settings text
    settings_text = f"<b>🔔 {get_message('notification_settings', language)}</b>\n\n"

    if notifications_enabled:
        status_text = get_message('notifications_enabled', language)
        toggle_text = get_message('disable_notifications', language)
        toggle_data = "disable_notifications"
    else:
        status_text = get_message('notifications_disabled', language)
        toggle_text = get_message('enable_notifications', language)
        toggle_data = "enable_notifications"

    settings_text += f"{get_message('notification_status', language)}: {status_text}"

    # Create keyboard
    keyboard = [
        [InlineKeyboardButton(toggle_text, callback_data=toggle_data)],
        [InlineKeyboardButton(get_message('back', language), callback_data="profile_settings")]
    ]

    # Send notification settings
    query.edit_message_text(
        settings_text,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def toggle_notifications_callback(update: Update, context: CallbackContext):
    """Handle toggling notifications on/off"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Toggle notifications
    if query.data == "enable_notifications":
        db.set_user_notification_preference(user_id, True)
        query.answer(get_message('notifications_now_enabled', language))
    else:  # disable_notifications
        db.set_user_notification_preference(user_id, False)
        query.answer(get_message('notifications_now_disabled', language))

    # Return to notification settings
    notification_settings_callback(update, context)

def get_language_name(language_code):
    """Get the full language name from code"""
    if language_code == 'en':
        return "🇬🇧 English"
    elif language_code == 'ru':
        return "🇷🇺 Русский"
    else:  # Default to Turkmen
        return "🇹🇲 Türkmen"
