from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext
from utils.constants import ParseMode
from utils.db import Database
from config import ADMIN_IDS, OWNER_ID

def is_admin(user_id):
    """Check if a user is an admin"""
    return user_id in ADMIN_IDS

def clear_history_command(update: Update, context: CallbackContext):
    """Handle /clear_history command to clear all search history"""
    user_id = update.effective_user.id
    
    # Check if user is admin
    if not is_admin(user_id):
        update.message.reply_text("⛔ Bu komanda diňe administratorlar üçin elýeterlidir.")
        return
    
    # Create confirmation keyboard
    keyboard = [
        [InlineKeyboardButton("✅ Hawa, arassala", callback_data="confirm_clear_all_history")],
        [InlineKeyboardButton("❌ Ýok, ýatyr", callback_data="cancel_clear_history")]
    ]
    
    # Send confirmation message
    update.message.reply_text(
        "⚠️ <b>DUÝDURYŞ!</b> ⚠️\n\n"
        "Siz <b>ÄHLI</b> ulanyjylaryň gözleg taryhyny arassalamak isleýärsiňizmi?\n\n"
        "Bu hereket yzyna gaýtarylyp bilinmez!",
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def confirm_clear_all_history_callback(update: Update, context: CallbackContext):
    """Handle confirmation to clear all search history"""
    query = update.callback_query
    user_id = query.from_user.id
    
    # Check if user is admin
    if not is_admin(user_id):
        query.answer("⛔ Bu funksiýa diňe administratorlar üçin elýeterlidir.")
        return
    
    query.answer()
    
    # Show processing message
    query.edit_message_text(
        "🔄 <b>Gözleg taryhy arassalanýar...</b>\n\n"
        "Bu biraz wagt alyp biler. Garaşyň...",
        parse_mode=ParseMode.HTML
    )
    
    try:
        # Clear all search history
        db = Database()
        
        # Clear searches table
        db.cur.execute("DELETE FROM searches")
        
        # Clear search_history table if it exists
        db.cur.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='search_history'")
        if db.cur.fetchone():
            db.cur.execute("DELETE FROM search_history")
        
        # Reset search counts in users table
        db.cur.execute("UPDATE users SET searches_today = 0")
        
        # Check if total_searches column exists
        db.cur.execute("PRAGMA table_info(users)")
        columns = db.cur.fetchall()
        column_names = [column[1] for column in columns]
        
        if 'total_searches' in column_names:
            db.cur.execute("UPDATE users SET total_searches = 0")
        
        # Commit changes
        db.conn.commit()
        
        # Update message
        query.edit_message_text(
            "✅ <b>Gözleg taryhy üstünlikli arassalandy!</b>\n\n"
            "Ähli ulanyjylaryň gözleg taryhy arassalandy.",
            parse_mode=ParseMode.HTML
        )
        
        # Log the action
        print(f"All search history cleared by admin {user_id}")
        
    except Exception as e:
        # Show error message
        query.edit_message_text(
            f"❌ <b>Gözleg taryhyny arassalamakda ýalňyşlyk ýüze çykdy:</b>\n\n{e}",
            parse_mode=ParseMode.HTML
        )
        print(f"Error clearing search history: {e}")

def cancel_clear_history_callback(update: Update, context: CallbackContext):
    """Handle cancellation of clear history"""
    query = update.callback_query
    query.answer()
    
    # Update message
    query.edit_message_text(
        "❌ <b>Gözleg taryhyny arassalamak ýatyryldy.</b>\n\n"
        "Hiç hili üýtgeşme edilmedi.",
        parse_mode=ParseMode.HTML
    )
