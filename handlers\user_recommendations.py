"""
User recommendations module for providing personalized search suggestions
"""
import logging
from utils.db import Database
import datetime
from collections import Counter
from handlers.search_history_analysis import analyze_search_history

# Set up logging
logging.basicConfig(format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO)
logger = logging.getLogger(__name__)

def get_personalized_recommendations(user_id, limit=5):
    """
    Get personalized search recommendations for a user
    
    Args:
        user_id: User ID to get recommendations for
        limit: Maximum number of recommendations to return
        
    Returns:
        List of recommended search queries
    """
    try:
        # Get user's search history analysis
        analysis = analyze_search_history(user_id, days=90)
        
        # If no searches or error, return empty list
        if 'error' in analysis or analysis['total_searches'] == 0:
            return []
        
        # Get most common queries
        recommendations = [item['query'] for item in analysis['most_common_queries'][:limit]]
        
        return recommendations
    
    except Exception as e:
        logger.error(f"Error getting personalized recommendations: {e}")
        return []

def get_similar_user_recommendations(user_id, limit=5):
    """
    Get recommendations based on similar users' searches
    
    Args:
        user_id: User ID to get recommendations for
        limit: Maximum number of recommendations to return
        
    Returns:
        List of recommended search queries
    """
    try:
        db = Database()
        
        # Get user's search history
        db.cur.execute("""
            SELECT query
            FROM searches
            WHERE user_id = ?
            ORDER BY search_date DESC
            LIMIT 20
        """, (user_id,))
        
        user_queries = [row[0] for row in db.cur.fetchall()]
        
        # If user has no search history, return empty list
        if not user_queries:
            return []
        
        # Find users who searched for similar queries
        similar_users = set()
        for query in user_queries:
            db.cur.execute("""
                SELECT DISTINCT user_id
                FROM searches
                WHERE query = ? AND user_id != ?
                LIMIT 50
            """, (query, user_id))
            
            for (similar_user_id,) in db.cur.fetchall():
                similar_users.add(similar_user_id)
        
        # If no similar users found, return empty list
        if not similar_users:
            return []
        
        # Get searches from similar users
        all_queries = []
        for similar_user_id in similar_users:
            db.cur.execute("""
                SELECT query
                FROM searches
                WHERE user_id = ? AND query NOT IN ({})
                ORDER BY search_date DESC
                LIMIT 10
            """.format(','.join(['?'] * len(user_queries))), 
            (similar_user_id, *user_queries))
            
            all_queries.extend([row[0] for row in db.cur.fetchall()])
        
        # Count occurrences and get most common
        query_counts = Counter(all_queries)
        recommendations = [query for query, _ in query_counts.most_common(limit)]
        
        return recommendations
    
    except Exception as e:
        logger.error(f"Error getting similar user recommendations: {e}")
        return []

def get_trending_recommendations(days=7, limit=5):
    """
    Get recommendations based on trending searches
    
    Args:
        days: Number of days to look back
        limit: Maximum number of recommendations to return
        
    Returns:
        List of recommended search queries
    """
    try:
        db = Database()
        
        # Calculate date range
        end_date = datetime.datetime.now()
        start_date = end_date - datetime.timedelta(days=days)
        
        # Format dates for SQL query
        start_date_str = start_date.strftime('%Y-%m-%d %H:%M:%S')
        end_date_str = end_date.strftime('%Y-%m-%d %H:%M:%S')
        
        # Get trending searches
        db.cur.execute("""
            SELECT query, COUNT(*) as count
            FROM searches
            WHERE search_date BETWEEN ? AND ?
            GROUP BY query
            ORDER BY count DESC
            LIMIT ?
        """, (start_date_str, end_date_str, limit))
        
        trending = [row[0] for row in db.cur.fetchall()]
        
        return trending
    
    except Exception as e:
        logger.error(f"Error getting trending recommendations: {e}")
        return []

def get_combined_recommendations(user_id, limit=5):
    """
    Get combined recommendations from multiple sources
    
    Args:
        user_id: User ID to get recommendations for
        limit: Maximum number of recommendations to return
        
    Returns:
        List of recommended search queries
    """
    try:
        # Get recommendations from different sources
        personal_recs = get_personalized_recommendations(user_id, limit=3)
        similar_recs = get_similar_user_recommendations(user_id, limit=3)
        trending_recs = get_trending_recommendations(days=7, limit=3)
        
        # Combine and deduplicate
        all_recs = []
        
        # Add personal recommendations first (highest priority)
        all_recs.extend(personal_recs)
        
        # Add similar user recommendations if not already in list
        for rec in similar_recs:
            if rec not in all_recs:
                all_recs.append(rec)
        
        # Add trending recommendations if not already in list
        for rec in trending_recs:
            if rec not in all_recs:
                all_recs.append(rec)
        
        # Return limited number of recommendations
        return all_recs[:limit]
    
    except Exception as e:
        logger.error(f"Error getting combined recommendations: {e}")
        return []