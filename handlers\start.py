# Import telegram modules with try/except to avoid IDE warnings
try:
    from telegram import Update
    from telegram.ext import CallbackContext
except ImportError:
    # Define placeholder classes for type hints
    class Update: pass
    class CallbackContext: pass
from utils.db import Database
from utils.keyboards import get_language_keyboard, get_main_menu_keyboard
from utils.languages import get_message

def start_command(update: Update, context: CallbackContext):
    """Handle the /start command"""
    user = update.effective_user
    user_id = user.id
    username = user.username
    full_name = user.full_name

    # Initialize database
    db = Database()

    # Add user to database if not exists
    referrer_id = None
    if context.args and len(context.args) > 0:
        try:
            referrer_id = int(context.args[0])
            if referrer_id == user_id:  # User can't refer themselves
                referrer_id = None
        except ValueError:
            referrer_id = None

    # Add user to database and get whether this is a new user
    is_new_user = db.add_user(user_id, username, full_name, referrer_id)

    # Get user's language preference
    language = db.get_user_language(user_id)

    # Check if user is blocked
    user_info = db.get_user_info(user_id)
    if user_info and user_info.get('is_blocked', False):
        # User is blocked, send blocked message
        if language == 'ru':
            blocked_message = "⛔ <b>Ваш аккаунт заблокирован.</b>\n\nВы не можете использовать бота. Если вы считаете, что это ошибка, пожалуйста, свяжитесь с администратором."
        elif language == 'en':
            blocked_message = "⛔ <b>Your account has been blocked.</b>\n\nYou cannot use the bot. If you believe this is an error, please contact an administrator."
        else:
            blocked_message = "⛔ <b>Siziň hasabyňyz blokirlendi.</b>\n\nSiz boty ulanyp bilmersiňiz. Eger bu ýalňyşlyk diýip pikir edýän bolsaňyz, administrator bilen habarlaşyň."

        update.message.reply_text(blocked_message, parse_mode='HTML')
        return

    # If user has a language preference, show main menu
    if language:
        # Check for unread notifications
        unread_count = db.get_unread_notifications_count(user_id)

        # Get total users count - always updated in real-time
        total_users = db.get_total_users_count()

        # Check if phone is verified
        from handlers.phone_verification import is_phone_verified
        phone_verified = is_phone_verified(user_id)

        # Show main menu
        welcome_text = get_message('welcome', language)

        # Add notification info if there are unread notifications
        if unread_count > 0:
            if language == 'ru':
                notification_text = f"\n\n📬 У вас есть {unread_count} непрочитанных уведомлений!"
            elif language == 'en':
                notification_text = f"\n\n📬 You have {unread_count} unread notifications!"
            else:
                notification_text = f"\n\n📬 Siziň {unread_count} sany okalmedik bildirişiňiz bar!"
            welcome_text += notification_text

        # Add total users count to welcome message
        if language == 'ru':
            users_text = f"\n\n👥 Всего пользователей бота: {total_users}"
        elif language == 'en':
            users_text = f"\n\n👥 Total bot users: {total_users}"
        else:
            users_text = f"\n\n👥 Jemi ulanyjylar: {total_users}"
        welcome_text += users_text

        # If phone is not verified, request verification
        if not phone_verified:
            from handlers.phone_verification import request_phone_verification

            # Send a clear message about mandatory verification
            if language == 'ru':
                message = "⚠️ <b>Обязательная верификация</b>\n\nДля использования бота необходимо подтвердить свой номер телефона. Без верификации вы не сможете использовать функции поиска."
            elif language == 'en':
                message = "⚠️ <b>Mandatory verification</b>\n\nYou must verify your phone number to use the bot. Without verification, you cannot use search functions."
            else:  # Default to Turkmen
                message = "⚠️ <b>Hökmany tassyklama</b>\n\nBoty ulanmak üçin telefon belgiňizi tassyklamagyňyz zerurdyr. Tassyklamasyz gözleg funksiýalaryny ulanyp bilmersiňiz."

            update.message.reply_text(
                message,
                parse_mode='HTML'
            )

            # Request phone verification
            request_phone_verification(update, context)
        else:
            # Phone is verified, show normal welcome
            update.message.reply_text(
                welcome_text,
                reply_markup=get_main_menu_keyboard(language, user_id)
            )
    else:
        # Otherwise, show language selection
        update.message.reply_text(
            get_message('language_select'),
            reply_markup=get_language_keyboard()
        )