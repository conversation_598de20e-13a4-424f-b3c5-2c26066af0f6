from telegram import Update
from telegram.ext import CallbackContext
from utils.constants import ParseMode
import os
import datetime
import shutil
from config import ADMIN_IDS, OWNER_ID, SQLITE_DB_PATH

def is_admin(user_id):
    """Check if a user is an admin"""
    return user_id in ADMIN_IDS

def backup_command(update: Update, context: CallbackContext):
    """Handle /backup command to create a database backup"""
    user_id = update.effective_user.id
    
    # Check if user is admin
    if not is_admin(user_id):
        update.message.reply_text("⛔ Bu komanda diňe administratorlar üçin elýeterlidir.")
        return
    
    try:
        # Create backups directory if it doesn't exist
        backup_dir = "backups"
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
        
        # Create a timestamp for the backup filename
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"{backup_dir}/backup_{timestamp}.sqlite"
        
        # Copy the database file
        shutil.copy2(SQLITE_DB_PATH, backup_filename)
        
        # Send the backup file to the user
        with open(backup_filename, 'rb') as backup_file:
            update.message.reply_document(
                document=backup_file,
                filename=f"database_backup_{timestamp}.sqlite",
                caption=f"✅ <b>Maglumat bazasynyň ätiýaçlyk nusgasy üstünlikli döredildi!</b>\n\n"
                        f"📅 Sene: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                        f"💾 Faýl: {backup_filename}\n\n"
                        f"Bu faýly dikeltmek üçin /restore komandasyny ulanyp bilersiňiz.",
                parse_mode=ParseMode.HTML
            )
        
        # Log the backup
        print(f"Database backup created: {backup_filename} by user {user_id}")
        
    except Exception as e:
        update.message.reply_text(f"❌ Maglumat bazasynyň ätiýaçlyk nusgasyny döretmekde ýalňyşlyk ýüze çykdy: {e}")
        print(f"Error creating database backup: {e}")

def restore_command(update: Update, context: CallbackContext):
    """Handle /restore command to restore a database backup"""
    user_id = update.effective_user.id
    
    # Check if user is owner (only owner can restore)
    if user_id != OWNER_ID:
        update.message.reply_text("⛔ Bu komanda diňe bot eýesi üçin elýeterlidir.")
        return
    
    # Check if file_id is provided
    if not context.args or len(context.args) < 1:
        update.message.reply_text(
            "❌ Faýl ID-si görkezilmedi. Dogry formatda ýazyň: /restore [file_id]\n\n"
            "Faýl ID-sini almak üçin, öň döredilen ätiýaçlyk nusgasyny bot arkaly iberiň, "
            "soňra şol faýla jogap hökmünde /restore komandasyny ýazyň."
        )
        return
    
    # If the command is a reply to a document message, use that document
    if update.message.reply_to_message and update.message.reply_to_message.document:
        file_id = update.message.reply_to_message.document.file_id
    else:
        # Otherwise use the provided file_id
        file_id = context.args[0]
    
    try:
        # Send a message that restoration is in progress
        progress_message = update.message.reply_text(
            "🔄 <b>Maglumat bazasyny dikeltmek başlandy...</b>\n\n"
            "Bu biraz wagt alyp biler. Garaşyň...",
            parse_mode=ParseMode.HTML
        )
        
        # Download the file
        file = context.bot.get_file(file_id)
        restore_filename = f"restore_temp.sqlite"
        file.download(restore_filename)
        
        # Create a backup of the current database before restoring
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_before_restore = f"backups/before_restore_{timestamp}.sqlite"
        
        # Create backups directory if it doesn't exist
        if not os.path.exists("backups"):
            os.makedirs("backups")
        
        # Backup current database
        shutil.copy2(SQLITE_DB_PATH, backup_before_restore)
        
        # Close any open database connections
        from utils.db import Database
        db = Database()
        db.close()
        
        # Replace the current database with the restored one
        shutil.copy2(restore_filename, SQLITE_DB_PATH)
        
        # Remove the temporary file
        os.remove(restore_filename)
        
        # Update the progress message
        progress_message.edit_text(
            "✅ <b>Maglumat bazasy üstünlikli dikeldildi!</b>\n\n"
            f"📅 Sene: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            "⚠️ Boty täzeden işletmek maslahat berilýär. Täzeden işletmek üçin /restart komandasyny ulanyň.",
            parse_mode=ParseMode.HTML
        )
        
        # Log the restoration
        print(f"Database restored from backup by user {user_id}")
        
    except Exception as e:
        update.message.reply_text(f"❌ Maglumat bazasyny dikeltmekde ýalňyşlyk ýüze çykdy: {e}")
        print(f"Error restoring database: {e}")
