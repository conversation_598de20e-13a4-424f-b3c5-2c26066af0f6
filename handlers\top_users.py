from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext
from utils.db import Database
from utils.languages import get_message

def show_top_users_points(update: Update, _: CallbackContext):
    """Show top 100 users by points"""
    query = update.callback_query
    query.answer()

    user_id = update.effective_user.id

    # Get user's language
    db = Database()
    user_info = db.get_user_info(user_id)
    language = user_info.get('language', 'tm')

    # Get top users by points
    top_users = db.get_top_users_by_points(100)

    # Format the message based on language
    if language == 'ru':
        message = "💰 <b>ТОП 100 пользователей по баллам</b>\n\n"
    elif language == 'en':
        message = "💰 <b>TOP 100 users by points</b>\n\n"
    else:
        message = "💰 <b>Iň köp baly bar 100 ulanyjy</b>\n\n"

    # Add top users to the message
    for i, user in enumerate(top_users, 1):
        if i <= 3:
            # Special formatting for top 3
            medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉"
            message += f"{medal} {i}) {user['username']} - {user['points']:,} bal\n"
        else:
            message += f"{i}) {user['username']} - {user['points']:,} bal\n"

    # Create keyboard
    keyboard = [
        [InlineKeyboardButton("🔄 " + get_message('refresh', language), callback_data="top_points")],
        [InlineKeyboardButton("👥 " + get_message('top_referrals', language), callback_data="top_referrals")],
        [InlineKeyboardButton("🔙 " + get_message('back', language), callback_data="main_menu")]
    ]

    # Send or edit message
    try:
        query.edit_message_text(
            message,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode="HTML"
        )
    except Exception as e:
        print(f"Error updating top points message: {e}")
        # If edit fails, try to send a new message
        try:
            query.message.reply_text(
                message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode="HTML"
            )
        except Exception as e2:
            print(f"Error sending new top points message: {e2}")

def show_top_users_referrals(update: Update, _: CallbackContext):
    """Show top 100 users by referrals"""
    query = update.callback_query
    query.answer()

    user_id = update.effective_user.id

    # Get user's language
    db = Database()
    user_info = db.get_user_info(user_id)
    language = user_info.get('language', 'tm')

    # Get top users by referrals
    top_referrers = db.get_top_users_by_referrals(100)

    # Format the message based on language
    if language == 'ru':
        message = "👥 <b>ТОП 100 пользователей по рефералам</b>\n\n"
    elif language == 'en':
        message = "👥 <b>TOP 100 users by referrals</b>\n\n"
    else:
        message = "👥 <b>Iň köp ulanyjy çagyran 100 ulanyjy</b>\n\n"

    # Add top referrers to the message
    for i, user in enumerate(top_referrers, 1):
        if i <= 3:
            # Special formatting for top 3
            medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉"
            message += f"{medal} {i}) {user['username']} - {user['referrals']} referal\n"
        else:
            message += f"{i}) {user['username']} - {user['referrals']} referal\n"

    # Create keyboard
    keyboard = [
        [InlineKeyboardButton("🔄 " + get_message('refresh', language), callback_data="top_referrals")],
        [InlineKeyboardButton("💰 " + get_message('top_points', language), callback_data="top_points")],
        [InlineKeyboardButton("🔙 " + get_message('back', language), callback_data="main_menu")]
    ]

    # Send or edit message
    try:
        query.edit_message_text(
            message,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode="HTML"
        )
    except Exception as e:
        print(f"Error updating top referrals message: {e}")
        # If edit fails, try to send a new message
        try:
            query.message.reply_text(
                message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode="HTML"
            )
        except Exception as e2:
            print(f"Error sending new top referrals message: {e2}")
