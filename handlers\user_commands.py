from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from utils.constants import ParseMode
from telegram.ext import CallbackContext
from utils.keyboards import get_back_button
from utils.db import Database
from utils.languages import get_message
from config import ADMIN_IDS, OWNER_ID

def user_commands_command(update: Update, context: CallbackContext):
    """Handle /commands command to show all available user commands"""
    user_id = update.effective_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Check if user is admin
    is_admin = user_id in ADMIN_IDS

    # Check if user is owner
    is_owner = user_id == OWNER_ID

    # Check if user is VIP
    user_info = db.get_user_info(user_id)
    is_vip = user_info.get('is_vip', False)

    if is_owner:
        # Show owner commands
        from handlers.admin_commands import owner_commands_command
        return owner_commands_command(update, context)
    elif is_admin:
        # Show admin commands
        from handlers.admin_commands import admin_commands_command
        return admin_commands_command(update, context)
    elif is_vip:
        # Show VIP commands
        return vip_commands_command(update, context)

    # Create a list of all user commands with descriptions
    if language == 'ru':
        commands_text = "<b>📋 Доступные команды</b>\n\n"

        # General commands
        commands_text += "<b>🔍 Команды поиска:</b>\n"
        commands_text += "/search [запрос] - Выполнить поиск\n"
        commands_text += "   <i>Пример: /search 99361234567</i>\n\n"

        # Profile commands
        commands_text += "<b>👤 Команды профиля:</b>\n"
        commands_text += "/profile - Просмотр профиля\n"
        commands_text += "/settings - Открыть настройки\n"
        commands_text += "/language - Выбрать язык\n\n"

        # History commands
        commands_text += "<b>📖 Команды истории:</b>\n"
        commands_text += "/history - Показать историю поиска\n"
        commands_text += "/favorites - Показать избранные результаты\n"
        commands_text += "/export - Экспорт истории поиска\n\n"

        # Other commands
        commands_text += "<b>ℹ️ Другие команды:</b>\n"
        commands_text += "/start - Перезапустить бота\n"
        commands_text += "/help - Показать помощь\n"
        commands_text += "/faq - Часто задаваемые вопросы\n"
        commands_text += "/support - Связаться с технической поддержкой\n"
        commands_text += "/promo - Использовать промо-код\n"
        commands_text += "/vip - Информация о VIP статусе\n"
        commands_text += "/notifications - Показать уведомления\n"
        commands_text += "/ping - Проверить работу бота\n\n"

        commands_text += "⭐ <b>Приобретите VIP статус, чтобы получить больше функций!</b>"
    elif language == 'en':
        commands_text = "<b>📋 Available Commands</b>\n\n"

        # General commands
        commands_text += "<b>🔍 Search Commands:</b>\n"
        commands_text += "/search [query] - Perform a search\n"
        commands_text += "   <i>Example: /search 99361234567</i>\n\n"

        # Profile commands
        commands_text += "<b>👤 Profile Commands:</b>\n"
        commands_text += "/profile - View your profile\n"
        commands_text += "/settings - Open settings\n"
        commands_text += "/language - Select language\n\n"

        # History commands
        commands_text += "<b>📖 History Commands:</b>\n"
        commands_text += "/history - Show search history\n"
        commands_text += "/favorites - Show favorite results\n"
        commands_text += "/export - Export search history\n\n"

        # Other commands
        commands_text += "<b>ℹ️ Other Commands:</b>\n"
        commands_text += "/start - Restart the bot\n"
        commands_text += "/help - Show help\n"
        commands_text += "/faq - Frequently asked questions\n"
        commands_text += "/support - Contact technical support\n"
        commands_text += "/promo - Use promo code\n"
        commands_text += "/vip - VIP status information\n"
        commands_text += "/notifications - Show notifications\n"
        commands_text += "/ping - Check bot operation\n\n"

        commands_text += "⭐ <b>Purchase VIP status to get more features!</b>"
    else:
        commands_text = "<b>📋 Elýeterli Komandalar</b>\n\n"

        # General commands
        commands_text += "<b>🔍 Gözleg Komandalary:</b>\n"
        commands_text += "/search [query] - Gözleg geçirmek\n"
        commands_text += "   <i>Mysal: /search 99361234567</i>\n\n"

        # Profile commands
        commands_text += "<b>👤 Profil Komandalary:</b>\n"
        commands_text += "/profile - Profiliňizi görkezmek\n"
        commands_text += "/settings - Sazlamalary açmak\n"
        commands_text += "/language - Dil saýlamak\n\n"

        # History commands
        commands_text += "<b>📖 Taryh Komandalary:</b>\n"
        commands_text += "/history - Gözleg taryhy görkezmek\n"
        commands_text += "/favorites - Saýlanan netijeleri görkezmek\n"
        commands_text += "/export - Gözleg taryhy eksport etmek\n\n"

        # Other commands
        commands_text += "<b>ℹ️ Beýleki Komandalar:</b>\n"
        commands_text += "/start - Boty täzeden başlatmak\n"
        commands_text += "/help - Kömek görkezmek\n"
        commands_text += "/faq - Köp soralýan soraglar\n"
        commands_text += "/support - Tehniki goldaw bilen habarlaşmak\n"
        commands_text += "/promo - Promo kod ulanmak\n"
        commands_text += "/vip - VIP statusy barada maglumat\n"
        commands_text += "/notifications - Bildirişleri görkezmek\n"
        commands_text += "/ping - Botyň işleýändigini barlamak\n\n"

        commands_text += "⭐ <b>VIP statusy satyn alyp, has köp funksiýalara eýe boluň!</b>"

    # Send the commands list
    update.message.reply_text(
        commands_text,
        parse_mode=ParseMode.HTML
    )

def vip_commands_command(update: Update, context: CallbackContext):
    """Handle /commands command for VIP users"""
    user_id = update.effective_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Create a list of all VIP commands with descriptions
    if language == 'ru':
        commands_text = "<b>⭐ VIP Команды</b>\n\n"

        # General commands
        commands_text += "<b>🔍 Команды поиска:</b>\n"
        commands_text += "/search [запрос] - Выполнить поиск\n"
        commands_text += "   <i>Пример: /search 99361234567</i>\n"
        commands_text += "/bulk [запросы] - Массовый поиск\n"
        commands_text += "   <i>Пример: /bulk 99361234567, 99365432100</i>\n\n"

        # Profile commands
        commands_text += "<b>👤 Команды профиля:</b>\n"
        commands_text += "/profile - Просмотр профиля\n"
        commands_text += "/settings - Открыть настройки\n"
        commands_text += "/language - Выбрать язык\n\n"

        # History commands
        commands_text += "<b>📖 Команды истории:</b>\n"
        commands_text += "/history - Показать историю поиска\n"
        commands_text += "/favorites - Показать избранные результаты\n"
        commands_text += "/export - Экспорт истории поиска\n\n"

        # Advanced commands
        commands_text += "<b>🔧 Расширенные команды:</b>\n"
        commands_text += "/filter - Установить фильтры поиска\n"
        commands_text += "/save_search - Сохранить поиск\n"
        commands_text += "/notes - Работа с заметками\n\n"

        # Other commands
        commands_text += "<b>ℹ️ Другие команды:</b>\n"
        commands_text += "/start - Перезапустить бота\n"
        commands_text += "/help - Показать помощь\n"
        commands_text += "/faq - Часто задаваемые вопросы\n"
        commands_text += "/support - Связаться с технической поддержкой\n"
        commands_text += "/promo - Использовать промо-код\n"
        commands_text += "/vip - Информация о VIP статусе\n"
        commands_text += "/notifications - Показать уведомления\n"
        commands_text += "/ping - Проверить работу бота\n\n"

        commands_text += "⭐ <b>Вы VIP пользователь! Все функции доступны.</b>"
    elif language == 'en':
        commands_text = "<b>⭐ VIP Commands</b>\n\n"

        # General commands
        commands_text += "<b>🔍 Search Commands:</b>\n"
        commands_text += "/search [query] - Perform a search\n"
        commands_text += "   <i>Example: /search 99361234567</i>\n"
        commands_text += "/bulk [queries] - Bulk search\n"
        commands_text += "   <i>Example: /bulk 99361234567, 99365432100</i>\n\n"

        # Profile commands
        commands_text += "<b>👤 Profile Commands:</b>\n"
        commands_text += "/profile - View your profile\n"
        commands_text += "/settings - Open settings\n"
        commands_text += "/language - Select language\n\n"

        # History commands
        commands_text += "<b>📖 History Commands:</b>\n"
        commands_text += "/history - Show search history\n"
        commands_text += "/favorites - Show favorite results\n"
        commands_text += "/export - Export search history\n\n"

        # Advanced commands
        commands_text += "<b>🔧 Advanced Commands:</b>\n"
        commands_text += "/filter - Set search filters\n"
        commands_text += "/save_search - Save search\n"
        commands_text += "/notes - Work with notes\n\n"

        # Other commands
        commands_text += "<b>ℹ️ Other Commands:</b>\n"
        commands_text += "/start - Restart the bot\n"
        commands_text += "/help - Show help\n"
        commands_text += "/faq - Frequently asked questions\n"
        commands_text += "/support - Contact technical support\n"
        commands_text += "/promo - Use promo code\n"
        commands_text += "/vip - VIP status information\n"
        commands_text += "/notifications - Show notifications\n"
        commands_text += "/ping - Check bot operation\n\n"

        commands_text += "⭐ <b>You are a VIP user! All features are available.</b>"
    else:
        commands_text = "<b>⭐ VIP Komandalar</b>\n\n"

        # General commands
        commands_text += "<b>🔍 Gözleg Komandalary:</b>\n"
        commands_text += "/search [query] - Gözleg geçirmek\n"
        commands_text += "   <i>Mysal: /search 99361234567</i>\n"
        commands_text += "/bulk [queries] - Köpçülikleýin gözleg geçirmek\n"
        commands_text += "   <i>Mysal: /bulk 99361234567, 99365432100</i>\n\n"

        # Profile commands
        commands_text += "<b>👤 Profil Komandalary:</b>\n"
        commands_text += "/profile - Profiliňizi görkezmek\n"
        commands_text += "/settings - Sazlamalary açmak\n"
        commands_text += "/language - Dil saýlamak\n\n"

        # History commands
        commands_text += "<b>📖 Taryh Komandalary:</b>\n"
        commands_text += "/history - Gözleg taryhy görkezmek\n"
        commands_text += "/favorites - Saýlanan netijeleri görkezmek\n"
        commands_text += "/export - Gözleg taryhy eksport etmek\n\n"

        # Advanced commands
        commands_text += "<b>🔧 Öňdebaryjy Komandalar:</b>\n"
        commands_text += "/filter - Gözleg filtrleri bellemek\n"
        commands_text += "/save_search - Gözlegi ýatda saklamak\n"
        commands_text += "/notes - Bellikler bilen işlemek\n\n"

        # Other commands
        commands_text += "<b>ℹ️ Beýleki Komandalar:</b>\n"
        commands_text += "/start - Boty täzeden başlatmak\n"
        commands_text += "/help - Kömek görkezmek\n"
        commands_text += "/faq - Köp soralýan soraglar\n"
        commands_text += "/support - Tehniki goldaw bilen habarlaşmak\n"
        commands_text += "/promo - Promo kod ulanmak\n"
        commands_text += "/vip - VIP statusy barada maglumat\n"
        commands_text += "/notifications - Bildirişleri görkezmek\n"
        commands_text += "/ping - Botyň işleýändigini barlamak\n\n"

        commands_text += "⭐ <b>Siz VIP ulanyjysyňyz! Ähli funksiýalar elýeterlidir.</b>"

    # Send the commands list
    update.message.reply_text(
        commands_text,
        parse_mode=ParseMode.HTML
    )

def user_commands_callback(update: Update, context: CallbackContext):
    """Handle user_commands callback query"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Check if user is admin
    is_admin = user_id in ADMIN_IDS

    # Check if user is owner
    is_owner = user_id == OWNER_ID

    # Check if user is VIP
    user_info = db.get_user_info(user_id)
    is_vip = user_info.get('is_vip', False)

    if is_owner:
        # Show owner commands
        from handlers.admin_commands import owner_commands_callback
        return owner_commands_callback(update, context)
    elif is_admin:
        # Show admin commands
        from handlers.admin_commands import admin_commands_callback
        return admin_commands_callback(update, context)
    elif is_vip:
        # Show VIP commands
        return vip_commands_callback(update, context)

    # Create a list of all user commands with descriptions
    if language == 'ru':
        commands_text = "<b>📋 Доступные команды</b>\n\n"

        # General commands
        commands_text += "<b>🔍 Команды поиска:</b>\n"
        commands_text += "/search [запрос] - Выполнить поиск\n"
        commands_text += "   <i>Пример: /search 99361234567</i>\n\n"

        # Profile commands
        commands_text += "<b>👤 Команды профиля:</b>\n"
        commands_text += "/profile - Просмотр профиля\n"
        commands_text += "/settings - Открыть настройки\n"
        commands_text += "/language - Выбрать язык\n\n"

        # History commands
        commands_text += "<b>📖 Команды истории:</b>\n"
        commands_text += "/history - Показать историю поиска\n"
        commands_text += "/favorites - Показать избранные результаты\n"
        commands_text += "/export - Экспорт истории поиска\n\n"

        # Other commands
        commands_text += "<b>ℹ️ Другие команды:</b>\n"
        commands_text += "/start - Перезапустить бота\n"
        commands_text += "/help - Показать помощь\n"
        commands_text += "/faq - Часто задаваемые вопросы\n"
        commands_text += "/support - Связаться с технической поддержкой\n"
        commands_text += "/promo - Использовать промо-код\n"
        commands_text += "/vip - Информация о VIP статусе\n"
        commands_text += "/notifications - Показать уведомления\n"
        commands_text += "/ping - Проверить работу бота\n\n"

        commands_text += "⭐ <b>Приобретите VIP статус, чтобы получить больше функций!</b>"

        # Create keyboard
        keyboard = [
            [InlineKeyboardButton("🔙 Назад", callback_data="main_menu")]
        ]
    elif language == 'en':
        commands_text = "<b>📋 Available Commands</b>\n\n"

        # General commands
        commands_text += "<b>🔍 Search Commands:</b>\n"
        commands_text += "/search [query] - Perform a search\n"
        commands_text += "   <i>Example: /search 99361234567</i>\n\n"

        # Profile commands
        commands_text += "<b>👤 Profile Commands:</b>\n"
        commands_text += "/profile - View your profile\n"
        commands_text += "/settings - Open settings\n"
        commands_text += "/language - Select language\n\n"

        # History commands
        commands_text += "<b>📖 History Commands:</b>\n"
        commands_text += "/history - Show search history\n"
        commands_text += "/favorites - Show favorite results\n"
        commands_text += "/export - Export search history\n\n"

        # Other commands
        commands_text += "<b>ℹ️ Other Commands:</b>\n"
        commands_text += "/start - Restart the bot\n"
        commands_text += "/help - Show help\n"
        commands_text += "/faq - Frequently asked questions\n"
        commands_text += "/support - Contact technical support\n"
        commands_text += "/promo - Use promo code\n"
        commands_text += "/vip - VIP status information\n"
        commands_text += "/notifications - Show notifications\n"
        commands_text += "/ping - Check bot operation\n\n"

        commands_text += "⭐ <b>Purchase VIP status to get more features!</b>"

        # Create keyboard
        keyboard = [
            [InlineKeyboardButton("🔙 Back", callback_data="main_menu")]
        ]
    else:
        commands_text = "<b>📋 Elýeterli Komandalar</b>\n\n"

        # General commands
        commands_text += "<b>🔍 Gözleg Komandalary:</b>\n"
        commands_text += "/search [query] - Gözleg geçirmek\n"
        commands_text += "   <i>Mysal: /search 99361234567</i>\n\n"

        # Profile commands
        commands_text += "<b>👤 Profil Komandalary:</b>\n"
        commands_text += "/profile - Profiliňizi görkezmek\n"
        commands_text += "/settings - Sazlamalary açmak\n"
        commands_text += "/language - Dil saýlamak\n\n"

        # History commands
        commands_text += "<b>📖 Taryh Komandalary:</b>\n"
        commands_text += "/history - Gözleg taryhy görkezmek\n"
        commands_text += "/favorites - Saýlanan netijeleri görkezmek\n"
        commands_text += "/export - Gözleg taryhy eksport etmek\n\n"

        # Other commands
        commands_text += "<b>ℹ️ Beýleki Komandalar:</b>\n"
        commands_text += "/start - Boty täzeden başlatmak\n"
        commands_text += "/help - Kömek görkezmek\n"
        commands_text += "/faq - Köp soralýan soraglar\n"
        commands_text += "/support - Tehniki goldaw bilen habarlaşmak\n"
        commands_text += "/promo - Promo kod ulanmak\n"
        commands_text += "/vip - VIP statusy barada maglumat\n"
        commands_text += "/notifications - Bildirişleri görkezmek\n"
        commands_text += "/ping - Botyň işleýändigini barlamak\n\n"

        commands_text += "⭐ <b>VIP statusy satyn alyp, has köp funksiýalara eýe boluň!</b>"

        # Create keyboard
        keyboard = [
            [InlineKeyboardButton("🔙 Yza", callback_data="main_menu")]
        ]



    # Send the commands list
    query.edit_message_text(
        commands_text,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def vip_commands_callback(update: Update, context: CallbackContext):
    """Handle vip_commands callback query"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Create a list of all VIP commands with descriptions
    if language == 'ru':
        commands_text = "<b>⭐ VIP Команды</b>\n\n"

        # General commands
        commands_text += "<b>🔍 Команды поиска:</b>\n"
        commands_text += "/search [запрос] - Выполнить поиск\n"
        commands_text += "   <i>Пример: /search 99361234567</i>\n"
        commands_text += "/bulk [запросы] - Массовый поиск\n"
        commands_text += "   <i>Пример: /bulk 99361234567, 99365432100</i>\n\n"

        # Profile commands
        commands_text += "<b>👤 Команды профиля:</b>\n"
        commands_text += "/profile - Просмотр профиля\n"
        commands_text += "/settings - Открыть настройки\n"
        commands_text += "/language - Выбрать язык\n\n"

        # History commands
        commands_text += "<b>📖 Команды истории:</b>\n"
        commands_text += "/history - Показать историю поиска\n"
        commands_text += "/favorites - Показать избранные результаты\n"
        commands_text += "/export - Экспорт истории поиска\n\n"

        # Advanced commands
        commands_text += "<b>🔧 Расширенные команды:</b>\n"
        commands_text += "/filter - Установить фильтры поиска\n"
        commands_text += "/save_search - Сохранить поиск\n"
        commands_text += "/notes - Работа с заметками\n\n"

        # Other commands
        commands_text += "<b>ℹ️ Другие команды:</b>\n"
        commands_text += "/start - Перезапустить бота\n"
        commands_text += "/help - Показать помощь\n"
        commands_text += "/faq - Часто задаваемые вопросы\n"
        commands_text += "/support - Связаться с технической поддержкой\n"
        commands_text += "/promo - Использовать промо-код\n"
        commands_text += "/vip - Информация о VIP статусе\n"
        commands_text += "/notifications - Показать уведомления\n"
        commands_text += "/ping - Проверить работу бота\n\n"

        commands_text += "⭐ <b>Вы VIP пользователь! Все функции доступны.</b>"

        # Create keyboard
        keyboard = [
            [InlineKeyboardButton("🔙 Назад", callback_data="main_menu")]
        ]
    elif language == 'en':
        commands_text = "<b>⭐ VIP Commands</b>\n\n"

        # General commands
        commands_text += "<b>🔍 Search Commands:</b>\n"
        commands_text += "/search [query] - Perform a search\n"
        commands_text += "   <i>Example: /search 99361234567</i>\n"
        commands_text += "/bulk [queries] - Bulk search\n"
        commands_text += "   <i>Example: /bulk 99361234567, 99365432100</i>\n\n"

        # Profile commands
        commands_text += "<b>👤 Profile Commands:</b>\n"
        commands_text += "/profile - View your profile\n"
        commands_text += "/settings - Open settings\n"
        commands_text += "/language - Select language\n\n"

        # History commands
        commands_text += "<b>📖 History Commands:</b>\n"
        commands_text += "/history - Show search history\n"
        commands_text += "/favorites - Show favorite results\n"
        commands_text += "/export - Export search history\n\n"

        # Advanced commands
        commands_text += "<b>🔧 Advanced Commands:</b>\n"
        commands_text += "/filter - Set search filters\n"
        commands_text += "/save_search - Save search\n"
        commands_text += "/notes - Work with notes\n\n"

        # Other commands
        commands_text += "<b>ℹ️ Other Commands:</b>\n"
        commands_text += "/start - Restart the bot\n"
        commands_text += "/help - Show help\n"
        commands_text += "/faq - Frequently asked questions\n"
        commands_text += "/support - Contact technical support\n"
        commands_text += "/promo - Use promo code\n"
        commands_text += "/vip - VIP status information\n"
        commands_text += "/notifications - Show notifications\n"
        commands_text += "/ping - Check bot operation\n\n"

        commands_text += "⭐ <b>You are a VIP user! All features are available.</b>"

        # Create keyboard
        keyboard = [
            [InlineKeyboardButton("🔙 Back", callback_data="main_menu")]
        ]
    else:
        commands_text = "<b>⭐ VIP Komandalar</b>\n\n"

        # General commands
        commands_text += "<b>🔍 Gözleg Komandalary:</b>\n"
        commands_text += "/search [query] - Gözleg geçirmek\n"
        commands_text += "   <i>Mysal: /search 99361234567</i>\n"
        commands_text += "/bulk [queries] - Köpçülikleýin gözleg geçirmek\n"
        commands_text += "   <i>Mysal: /bulk 99361234567, 99365432100</i>\n\n"

        # Profile commands
        commands_text += "<b>👤 Profil Komandalary:</b>\n"
        commands_text += "/profile - Profiliňizi görkezmek\n"
        commands_text += "/settings - Sazlamalary açmak\n"
        commands_text += "/language - Dil saýlamak\n\n"

        # History commands
        commands_text += "<b>📖 Taryh Komandalary:</b>\n"
        commands_text += "/history - Gözleg taryhy görkezmek\n"
        commands_text += "/favorites - Saýlanan netijeleri görkezmek\n"
        commands_text += "/export - Gözleg taryhy eksport etmek\n\n"

        # Advanced commands
        commands_text += "<b>🔧 Öňdebaryjy Komandalar:</b>\n"
        commands_text += "/filter - Gözleg filtrleri bellemek\n"
        commands_text += "/save_search - Gözlegi ýatda saklamak\n"
        commands_text += "/notes - Bellikler bilen işlemek\n\n"

        # Other commands
        commands_text += "<b>ℹ️ Beýleki Komandalar:</b>\n"
        commands_text += "/start - Boty täzeden başlatmak\n"
        commands_text += "/help - Kömek görkezmek\n"
        commands_text += "/faq - Köp soralýan soraglar\n"
        commands_text += "/support - Tehniki goldaw bilen habarlaşmak\n"
        commands_text += "/promo - Promo kod ulanmak\n"
        commands_text += "/vip - VIP statusy barada maglumat\n"
        commands_text += "/notifications - Bildirişleri görkezmek\n"
        commands_text += "/ping - Botyň işleýändigini barlamak\n\n"

        commands_text += "⭐ <b>Siz VIP ulanyjysyňyz! Ähli funksiýalar elýeterlidir.</b>"

        # Create keyboard
        keyboard = [
            [InlineKeyboardButton("🔙 Yza", callback_data="main_menu")]
        ]



    # Send the commands list
    query.edit_message_text(
        commands_text,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )
