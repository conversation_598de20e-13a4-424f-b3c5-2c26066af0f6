"""
Handler for viewing new users
"""
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from utils.constants import ParseMode
from telegram.ext import CallbackContext
from utils.db import Database
from utils.languages import get_message
from config import ADMIN_IDS, OWNER_ID
import datetime

def new_users_command(update: Update, context: CallbackContext):
    """Handle /new_users command to view new users in the last 30 days"""
    user_id = update.effective_user.id

    # Check if user is owner
    if user_id != OWNER_ID:
        update.message.reply_text("Bu funksiýa diňe bot eýesi üçin elýeterlidir.")
        return

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get days parameter if provided
    days = 30  # Default to 30 days
    if context.args and len(context.args) > 0:
        try:
            days = int(context.args[0])
            if days <= 0:
                days = 30
        except ValueError:
            days = 30

    # Get new users from the last X days
    new_users = db.get_new_users(days)

    if not new_users:
        update.message.reply_text(
            f"Soňky {days} günde täze ulanyjylar ýok.",
            parse_mode=ParseMode.HTML
        )
        return

    # Format new users message with enhanced details
    message = f"<b>📊 Soňky {days} günde täze ulanyjylar:</b>\n\n"

    for i, user in enumerate(new_users, 1):
        user_id = user.get('user_id', '')
        username = user.get('username', '')
        if username and not username.startswith('@'):
            username = f"@{username}"
        full_name = user.get('full_name', '')
        phone_number = user.get('phone_number', 'Ýok')
        joined_date = user.get('joined_date', '')

        # Get additional user info if available
        try:
            user_info = db.get_user_info(user_id)
            total_searches = user_info.get('total_searches', 0)
            is_vip = user_info.get('is_vip', False)
            vip_status = "✅ Hawa" if is_vip else "❌ Ýok"
        except Exception as e:
            print(f"Error getting additional user info: {e}")
            total_searches = 0
            vip_status = "❌ Ýok"

        message += f"{i}. <b>ID:</b> <code>{user_id}</code>\n"
        message += f"   <b>Ady:</b> {full_name}\n"
        message += f"   <b>Ulanyjy ady:</b> {username or 'Ýok'}\n"
        message += f"   <b>Telefon:</b> {phone_number}\n"
        message += f"   <b>Goşulan wagty:</b> {joined_date}\n"
        message += f"   <b>Gözlegler:</b> {total_searches}\n"
        message += f"   <b>VIP:</b> {vip_status}\n\n"

        # Limit message length
        if len(message) > 3500:
            message += f"...we ýene {len(new_users) - i} sany ulanyjy"
            break

    # Create keyboard with more options for owner
    keyboard = [
        [InlineKeyboardButton(f"Soňky 7 gün", callback_data="new_users_7")],
        [InlineKeyboardButton(f"Soňky 30 gün", callback_data="new_users_30")],
        [InlineKeyboardButton(f"Soňky 90 gün", callback_data="new_users_90")],
        [InlineKeyboardButton("📊 Jikme-jik statistika", callback_data="admin_stats")],
        [InlineKeyboardButton("🔙 Yza", callback_data="admin_panel")]
    ]

    update.message.reply_text(
        message,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode=ParseMode.HTML
    )

def new_users_callback(update: Update, context: CallbackContext):
    """Handle new users callback queries"""
    query = update.callback_query
    user_id = query.from_user.id

    # Check if user is owner
    if user_id != OWNER_ID:
        query.answer("Bu funksiýa diňe bot eýesi üçin elýeterlidir.")
        return

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get days from callback data
    days = 30  # Default
    if query.data == "new_users_7":
        days = 7
    elif query.data == "new_users_30":
        days = 30
    elif query.data == "new_users_90":
        days = 90

    # Get new users from the last X days
    new_users = db.get_new_users(days)

    if not new_users:
        query.edit_message_text(
            f"Soňky {days} günde täze ulanyjylar ýok.",
            parse_mode=ParseMode.HTML
        )
        return

    # Format new users message with enhanced details
    message = f"<b>📊 Soňky {days} günde täze ulanyjylar:</b>\n\n"

    for i, user in enumerate(new_users, 1):
        user_id = user.get('user_id', '')
        username = user.get('username', '')
        if username and not username.startswith('@'):
            username = f"@{username}"
        full_name = user.get('full_name', '')
        phone_number = user.get('phone_number', 'Ýok')
        joined_date = user.get('joined_date', '')

        # Get additional user info if available
        try:
            user_info = db.get_user_info(user_id)
            total_searches = user_info.get('total_searches', 0)
            is_vip = user_info.get('is_vip', False)
            vip_status = "✅ Hawa" if is_vip else "❌ Ýok"
        except Exception as e:
            print(f"Error getting additional user info: {e}")
            total_searches = 0
            vip_status = "❌ Ýok"

        message += f"{i}. <b>ID:</b> <code>{user_id}</code>\n"
        message += f"   <b>Ady:</b> {full_name}\n"
        message += f"   <b>Ulanyjy ady:</b> {username or 'Ýok'}\n"
        message += f"   <b>Telefon:</b> {phone_number}\n"
        message += f"   <b>Goşulan wagty:</b> {joined_date}\n"
        message += f"   <b>Gözlegler:</b> {total_searches}\n"
        message += f"   <b>VIP:</b> {vip_status}\n\n"

        # Limit message length
        if len(message) > 3500:
            message += f"...we ýene {len(new_users) - i} sany ulanyjy"
            break

    # Create keyboard with more options for owner
    keyboard = [
        [InlineKeyboardButton(f"Soňky 7 gün", callback_data="new_users_7")],
        [InlineKeyboardButton(f"Soňky 30 gün", callback_data="new_users_30")],
        [InlineKeyboardButton(f"Soňky 90 gün", callback_data="new_users_90")],
        [InlineKeyboardButton("📊 Jikme-jik statistika", callback_data="admin_stats")],
        [InlineKeyboardButton("🔙 Yza", callback_data="admin_panel")]
    ]

    query.edit_message_text(
        message,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode=ParseMode.HTML
    )
