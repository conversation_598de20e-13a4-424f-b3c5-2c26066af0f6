from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ParseMode
from telegram.ext import CallbackContext
from utils.db import Database
from utils.languages import get_message
from utils.keyboards import get_filter_keyboard

def filter_command(update: Update, context: CallbackContext):
    """Handle /filter command"""
    user_id = update.effective_user.id
    
    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)
    
    # Check if user is VIP
    user_info = db.get_user_info(user_id)
    is_vip = user_info.get('is_vip', False)
    
    if not is_vip:
        # Only VIP users can use filters
        keyboard = [
            [InlineKeyboardButton(get_message('vip_button', language), callback_data="vip_buy")],
            [InlineKeyboardButton(get_message('back', language), callback_data="main_menu")]
        ]
        
        update.message.reply_text(
            "⭐ Bu funksiýa diňe VIP ulanyjylar üçin elýeterlidir.\n\nVIP statusyny satyn alyp, has köp funksiýalara eýe bolu<PERSON>!",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
        return
    
    # Show filter options
    update.message.reply_text(
        get_message('filter_title', language),
        reply_markup=get_filter_keyboard(language)
    )
