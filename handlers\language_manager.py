from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext
from utils.db import Database
import json
import os

# Available languages
AVAILABLE_LANGUAGES = {
    'tm': 'Türkmen',
    'ru': 'Русский',
    'en': 'English',
    'tr': 'Türkçe',
    'az': 'Azərbaycan'
}

# Default language messages
DEFAULT_MESSAGES = {
    'welcome': "Welcome to the bot!",
    'language_changed': "Language changed successfully!",
    'back': "Back"
}

def get_language_keyboard():
    """Returns a keyboard with all available languages"""
    keyboard = []
    row = []
    
    # Create buttons for each language
    for i, (lang_code, lang_name) in enumerate(AVAILABLE_LANGUAGES.items()):
        # Add flag emoji based on language code
        if lang_code == 'tm':
            flag = "🇹🇲"
        elif lang_code == 'ru':
            flag = "🇷🇺"
        elif lang_code == 'en':
            flag = "🇬🇧"
        elif lang_code == 'tr':
            flag = "🇹🇷"
        elif lang_code == 'az':
            flag = "🇦🇿"
        else:
            flag = "🌐"
        
        # Add button to row
        row.append(InlineKeyboardButton(f"{flag} {lang_name}", callback_data=f"lang_{lang_code}"))
        
        # Create new row after every 2 buttons
        if (i + 1) % 2 == 0:
            keyboard.append(row)
            row = []
    
    # Add any remaining buttons
    if row:
        keyboard.append(row)
    
    return InlineKeyboardMarkup(keyboard)

def language_command(update: Update, context: CallbackContext):
    """Handle the /language command"""
    user_id = update.effective_user.id
    
    # Get user's current language
    db = Database()
    current_language = db.get_user_language(user_id)
    
    # Get language name
    current_language_name = AVAILABLE_LANGUAGES.get(current_language, "Unknown")
    
    # Create message based on current language
    if current_language == 'ru':
        message = f"🌐 <b>Выбор языка</b>\n\nТекущий язык: {current_language_name}\n\nВыберите язык из списка ниже:"
    elif current_language == 'en':
        message = f"🌐 <b>Language Selection</b>\n\nCurrent language: {current_language_name}\n\nSelect a language from the list below:"
    else:
        message = f"🌐 <b>Dil saýlamak</b>\n\nHäzirki dil: {current_language_name}\n\nAşakdaky sanawdan dil saýlaň:"
    
    # Send message with language keyboard
    update.message.reply_text(message, reply_markup=get_language_keyboard(), parse_mode='HTML')

def language_callback(update: Update, context: CallbackContext):
    """Handle language selection callbacks"""
    query = update.callback_query
    user_id = query.from_user.id
    callback_data = query.data
    
    # Extract language code from callback data
    lang_code = callback_data.split('_')[1]
    
    # Check if language is available
    if lang_code not in AVAILABLE_LANGUAGES:
        query.answer("This language is not available yet.")
        return
    
    # Update user's language preference
    db = Database()
    db.set_user_language(user_id, lang_code)
    
    # Get language name
    language_name = AVAILABLE_LANGUAGES.get(lang_code, "Unknown")
    
    # Create success message based on selected language
    if lang_code == 'ru':
        message = f"✅ Язык успешно изменен на {language_name}!"
    elif lang_code == 'en':
        message = f"✅ Language successfully changed to {language_name}!"
    elif lang_code == 'tr':
        message = f"✅ Dil başarıyla {language_name} olarak değiştirildi!"
    elif lang_code == 'az':
        message = f"✅ Dil uğurla {language_name} olaraq dəyişdirildi!"
    else:
        message = f"✅ Dil üstünlikli {language_name} diline üýtgedildi!"
    
    # Answer the callback query
    query.answer(message)
    
    # Update the message with main menu
    from utils.keyboards import get_main_menu_keyboard
    
    # Get welcome message in the new language
    from utils.languages import get_message
    welcome_text = get_message('welcome', lang_code)
    
    # Show main menu
    query.edit_message_text(
        welcome_text,
        reply_markup=get_main_menu_keyboard(lang_code, user_id),
        parse_mode='HTML'
    )

def load_language_file(lang_code):
    """Load language messages from file"""
    try:
        # Check if language file exists
        file_path = f"languages/{lang_code}.json"
        if not os.path.exists(file_path):
            return None
        
        # Load language file
        with open(file_path, 'r', encoding='utf-8') as file:
            return json.load(file)
    except Exception as e:
        print(f"Error loading language file: {e}")
        return None

def save_language_file(lang_code, messages):
    """Save language messages to file"""
    try:
        # Create languages directory if it doesn't exist
        if not os.path.exists('languages'):
            os.makedirs('languages')
        
        # Save language file
        with open(f"languages/{lang_code}.json", 'w', encoding='utf-8') as file:
            json.dump(messages, file, ensure_ascii=False, indent=4)
        
        return True
    except Exception as e:
        print(f"Error saving language file: {e}")
        return False

def add_language(lang_code, lang_name, messages=None):
    """Add a new language to the bot"""
    # Check if language code is valid
    if not lang_code or len(lang_code) != 2:
        return False, "Invalid language code. Language code must be 2 characters."
    
    # Check if language already exists
    if lang_code in AVAILABLE_LANGUAGES:
        return False, f"Language {lang_code} already exists."
    
    # Add language to available languages
    AVAILABLE_LANGUAGES[lang_code] = lang_name
    
    # Save language messages
    if messages:
        save_language_file(lang_code, messages)
    else:
        # Use default messages
        save_language_file(lang_code, DEFAULT_MESSAGES)
    
    return True, f"Language {lang_code} ({lang_name}) added successfully."

def remove_language(lang_code):
    """Remove a language from the bot"""
    # Check if language exists
    if lang_code not in AVAILABLE_LANGUAGES:
        return False, f"Language {lang_code} does not exist."
    
    # Check if it's a default language
    if lang_code in ['tm', 'ru', 'en']:
        return False, f"Cannot remove default language {lang_code}."
    
    # Remove language from available languages
    lang_name = AVAILABLE_LANGUAGES.pop(lang_code)
    
    # Remove language file
    try:
        file_path = f"languages/{lang_code}.json"
        if os.path.exists(file_path):
            os.remove(file_path)
    except Exception as e:
        print(f"Error removing language file: {e}")
    
    return True, f"Language {lang_code} ({lang_name}) removed successfully."