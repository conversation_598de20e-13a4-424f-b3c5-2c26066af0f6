from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from utils.constants import ParseMode
from telegram.ext import CallbackContext
from utils.db import Database
import re

def get_search_suggestions(user_id, query_text, max_suggestions=5):
    """Get search suggestions based on user's search history and popular searches"""
    db = Database()

    try:
        # Process query text
        query_text = query_text.lower().strip()

        suggestions = []
        seen = set()

        # 1. First, get user's own search history
        db.cur.execute("""
            SELECT query, search_type
            FROM search_history
            WHERE user_id = ?
            ORDER BY search_time DESC
            LIMIT 50
        """, (user_id,))

        user_search_history = db.cur.fetchall()

        # If query is too short, prioritize recent searches
        if len(query_text) < 2:
            # Get unique recent searches from user history
            for query, search_type in user_search_history:
                if query not in seen and len(suggestions) < max_suggestions:
                    suggestions.append((query, search_type))
                    seen.add(query)
        else:
            # Find matching queries in user history
            for query, search_type in user_search_history:
                # Check if query contains the search text
                if query_text in query.lower() and query not in seen and len(suggestions) < max_suggestions:
                    suggestions.append((query, search_type))
                    seen.add(query)

        # 2. If we still need more suggestions, get popular searches from all users
        if len(suggestions) < max_suggestions:
            remaining_slots = max_suggestions - len(suggestions)

            # Get popular searches
            db.cur.execute("""
                SELECT query, search_type, COUNT(*) as count
                FROM search_history
                GROUP BY query, search_type
                ORDER BY count DESC
                LIMIT 20
            """)

            popular_searches = db.cur.fetchall()

            # Add popular searches that match the query
            for query, search_type, _ in popular_searches:
                if (len(query_text) < 2 or query_text in query.lower()) and query not in seen and len(suggestions) < max_suggestions:
                    suggestions.append((query, search_type))
                    seen.add(query)

                    # Break if we have enough suggestions
                    if len(suggestions) >= max_suggestions:
                        break
                        
        # 3. Add personalized recommendations if we still need more suggestions
        if len(suggestions) < max_suggestions:
            try:
                from handlers.user_recommendations import get_combined_recommendations
                
                # Get personalized recommendations
                recommendations = get_combined_recommendations(user_id, limit=max_suggestions-len(suggestions))
                
                # Add recommendations that don't match existing suggestions
                for rec in recommendations:
                    # Determine search type based on content
                    if rec.isdigit():
                        search_type = 'phone'
                    else:
                        search_type = 'name'
                        
                    if rec not in seen and len(suggestions) < max_suggestions:
                        suggestions.append((rec, search_type))
                        seen.add(rec)
            except Exception as e:
                print(f"Error getting personalized recommendations: {e}")

        # 4. Add smart suggestions for phone numbers
        if len(query_text) >= 3 and query_text.isdigit() and len(suggestions) < max_suggestions:
            # If user is typing a phone number, suggest variations
            if len(query_text) <= 9:  # Partial phone number
                # Suggest with country code if not present
                if not query_text.startswith('993'):
                    suggestion = '993' + query_text
                    if suggestion not in seen:
                        suggestions.append((suggestion, 'phone'))
                        seen.add(suggestion)

        return suggestions

    except Exception as e:
        print(f"Error getting search suggestions: {e}")
        return []
    finally:
        db.close()

def format_suggestions_keyboard(suggestions, language='tm'):
    """Format suggestions as keyboard buttons"""
    keyboard = []

    for query, search_type in suggestions:
        # Add icon based on search type
        if search_type == 'phone':
            icon = "📱"
        elif search_type == 'name':
            icon = "👤"
        elif search_type == 'passport':
            icon = "📗"
        else:
            icon = "🔍"

        # Add button for each suggestion
        keyboard.append([InlineKeyboardButton(f"{icon} {query}", callback_data=f"suggest_{search_type}_{query}")])

    return keyboard

def handle_suggestion_callback(update: Update, context: CallbackContext):
    """Handle suggestion callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get suggestion data
    callback_data = query.data
    parts = callback_data.split('_', 2)

    if len(parts) < 3:
        query.answer("Invalid suggestion format")
        return

    search_type = parts[1]
    search_query = parts[2]

    # Answer callback query
    query.answer(f"Searching for: {search_query}")

    # Import search handler
    from handlers.search import handle_search

    # Create a mock update with the search query
    class MockMessage:
        def __init__(self, text, from_user, chat_id):
            self.text = text
            self.from_user = from_user
            self.chat_id = chat_id

        def reply_text(self, text, parse_mode=None, reply_markup=None):
            query.edit_message_text(text, parse_mode=parse_mode, reply_markup=reply_markup)

    class MockUpdate:
        def __init__(self, message):
            self.message = message
            self.effective_user = message.from_user

    # Create mock objects
    mock_message = MockMessage(search_query, query.from_user, query.message.chat_id)
    mock_update = MockUpdate(mock_message)

    # Set search type in context
    context.user_data['search_type'] = search_type

    # Call search handler with the mock update
    handle_search(mock_update, context)

def add_suggestions_to_message(message_text, user_id, query_text, language='tm'):
    """Add suggestions to a message if available"""
    # Get suggestions
    suggestions = get_search_suggestions(user_id, query_text)

    if not suggestions:
        return message_text, None

    # Format suggestions keyboard
    keyboard = format_suggestions_keyboard(suggestions, language)

    # Add suggestions text to message
    if language == 'ru':
        suggestions_text = "\n\n<b>🔍 Предыдущие поиски:</b>"
    elif language == 'en':
        suggestions_text = "\n\n<b>🔍 Previous searches:</b>"
    else:  # Default to Turkmen
        suggestions_text = "\n\n<b>🔍 Öňki gözlegler:</b>"

    return message_text + suggestions_text, InlineKeyboardMarkup(keyboard)
