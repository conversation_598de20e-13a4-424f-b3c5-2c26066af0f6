from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from utils.constants import ParseMode
from telegram.ext import CallbackContext
from utils.db import Database
from utils.languages import get_message
from config import ADMIN_IDS, OWNER_ID
from handlers.user_commands import vip_commands_command

def handle_set_vip_commands(update: Update, context: CallbackContext):
    """Handle different VIP setting commands"""
    # Force reload ADMIN_IDS from config to ensure it's up to date
    import importlib
    import config
    importlib.reload(config)
    # Get the latest ADMIN_IDS
    from config import ADMIN_IDS, OWNER_ID

    user_id = update.effective_user.id
    message_text = update.message.text

    # Check if user is admin
    if user_id not in ADMIN_IDS:
        update.message.reply_text("⚠️ Bu komanda diňe administratorlar üçin elýeterlidir.")
        return

    # Get database connection
    db = Database()

    # Print debug information
    print(f"Processing command: {message_text} from user {user_id}")

    # Handle set_vip_1 command (1 month VIP)
    if message_text.startswith('/set_vip_1'):
        try:
            # Parse command
            parts = message_text.split()
            if len(parts) != 2:
                update.message.reply_text("Invalid format. Use: /set_vip_1 [user_id]")
                return

            target_user_id = int(parts[1])

            # Check if user exists
            if not db.user_exists(target_user_id):
                update.message.reply_text(f"User with ID {target_user_id} does not exist.")
                return

            # Check if user already has VIP
            user_info = db.get_user_info(target_user_id)
            if user_info.get('is_vip', False):
                # Get VIP expiry date
                vip_expiry = db.get_vip_expiry_date(target_user_id)
                if vip_expiry:
                    update.message.reply_text(f"User {target_user_id} already has VIP status until {vip_expiry.get('expiry_date', 'unknown date')}.")
                    return

            # Set VIP status for 1 month (30 days)
            success = db.set_user_vip_status(target_user_id, True, 30)

            if success:
                # Send VIP welcome message
                send_vip_welcome_message(context, target_user_id, 30, 100)
                update.message.reply_text(f"✅ Successfully enabled 1-month VIP status for user {target_user_id}.")
                print(f"Set VIP status for user {target_user_id} for 30 days")
            else:
                update.message.reply_text(f"❌ Failed to update VIP status for user {target_user_id}.")
                print(f"Failed to set VIP status for user {target_user_id}")

        except ValueError:
            update.message.reply_text("Invalid user ID. Please use numbers only.")
        except Exception as e:
            update.message.reply_text(f"Error setting VIP status: {e}")
            print(f"Error setting VIP status: {e}")

    # Handle set_vip_3 command (3 months VIP)
    elif message_text.startswith('/set_vip_3'):
        try:
            # Parse command
            parts = message_text.split()
            if len(parts) != 2:
                update.message.reply_text("Invalid format. Use: /set_vip_3 [user_id]")
                return

            target_user_id = int(parts[1])

            # Check if user exists
            if not db.user_exists(target_user_id):
                update.message.reply_text(f"User with ID {target_user_id} does not exist.")
                return

            # Check if user already has VIP
            user_info = db.get_user_info(target_user_id)
            if user_info.get('is_vip', False):
                # Get VIP expiry date
                vip_expiry = db.get_vip_expiry_date(target_user_id)
                if vip_expiry:
                    update.message.reply_text(f"User {target_user_id} already has VIP status until {vip_expiry.get('expiry_date', 'unknown date')}.")
                    return

            # Set VIP status for 3 months (90 days)
            success = db.set_user_vip_status(target_user_id, True, 90)

            if success:
                # Send VIP welcome message
                send_vip_welcome_message(context, target_user_id, 90, 300)
                update.message.reply_text(f"✅ Successfully enabled 3-month VIP status for user {target_user_id}.")
                print(f"Set VIP status for user {target_user_id} for 90 days")
            else:
                update.message.reply_text(f"❌ Failed to update VIP status for user {target_user_id}.")
                print(f"Failed to set VIP status for user {target_user_id}")

        except ValueError:
            update.message.reply_text("Invalid user ID. Please use numbers only.")
        except Exception as e:
            update.message.reply_text(f"Error setting VIP status: {e}")
            print(f"Error setting VIP status: {e}")

    # Handle set_vip_6 command (6 months VIP)
    elif message_text.startswith('/set_vip_6'):
        try:
            # Parse command
            parts = message_text.split()
            if len(parts) != 2:
                update.message.reply_text("Invalid format. Use: /set_vip_6 [user_id]")
                return

            target_user_id = int(parts[1])

            # Check if user exists
            if not db.user_exists(target_user_id):
                update.message.reply_text(f"User with ID {target_user_id} does not exist.")
                return

            # Check if user already has VIP
            user_info = db.get_user_info(target_user_id)
            if user_info.get('is_vip', False):
                # Get VIP expiry date
                vip_expiry = db.get_vip_expiry_date(target_user_id)
                if vip_expiry:
                    update.message.reply_text(f"User {target_user_id} already has VIP status until {vip_expiry.get('expiry_date', 'unknown date')}.")
                    return

            # Set VIP status for 6 months (180 days)
            success = db.set_user_vip_status(target_user_id, True, 180)

            if success:
                # Send VIP welcome message
                send_vip_welcome_message(context, target_user_id, 180, 1000)
                update.message.reply_text(f"✅ Successfully enabled 6-month VIP status for user {target_user_id}.")
                print(f"Set VIP status for user {target_user_id} for 180 days")
            else:
                update.message.reply_text(f"❌ Failed to update VIP status for user {target_user_id}.")
                print(f"Failed to set VIP status for user {target_user_id}")

        except ValueError:
            update.message.reply_text("Invalid user ID. Please use numbers only.")
        except Exception as e:
            update.message.reply_text(f"Error setting VIP status: {e}")
            print(f"Error setting VIP status: {e}")

    # Handle new_admin command (owner only)
    elif message_text.startswith('/new_admin'):
        # Check if user is owner
        if user_id != OWNER_ID:
            update.message.reply_text("⚠️ Bu komanda diňe bot eýesi üçin elýeterlidir.")
            return

        try:
            # Parse command
            parts = message_text.split()
            if len(parts) != 2:
                update.message.reply_text("Invalid format. Use: /new_admin [user_id]")
                return

            target_user_id = int(parts[1])

            # Check if user exists
            if not db.user_exists(target_user_id):
                update.message.reply_text(f"User with ID {target_user_id} does not exist.")
                return

            # Check if user is already an admin
            if target_user_id in ADMIN_IDS:
                update.message.reply_text(f"User {target_user_id} is already an admin.")
                return

            # Add user to admin list in config.py
            try:
                # Read config.py
                with open('config.py', 'r', encoding='utf-8') as f:
                    config_content = f.read()

                # Find ADMIN_IDS line
                import re
                admin_ids_pattern = r'ADMIN_IDS\s*=\s*\[(.*?)\]'
                match = re.search(admin_ids_pattern, config_content, re.DOTALL)

                if match:
                    # Get current admin IDs
                    admin_ids_str = match.group(1)

                    # Add new admin ID
                    new_admin_ids_str = admin_ids_str
                    if admin_ids_str.strip():
                        new_admin_ids_str = admin_ids_str + f", {target_user_id}"
                    else:
                        new_admin_ids_str = str(target_user_id)

                    # Replace in config content
                    new_config_content = config_content.replace(
                        f"ADMIN_IDS = [{admin_ids_str}]",
                        f"ADMIN_IDS = [{new_admin_ids_str}]"
                    )

                    # Write back to config.py
                    with open('config.py', 'w', encoding='utf-8') as f:
                        f.write(new_config_content)

                    # Update ADMIN_IDS in memory
                    import importlib
                    import config
                    importlib.reload(config)
                    # Get the latest ADMIN_IDS
                    from config import ADMIN_IDS as updated_admin_ids
                    # Add the new admin to the list
                    if target_user_id not in updated_admin_ids:
                        updated_admin_ids.append(target_user_id)

                    # Send confirmation
                    update.message.reply_text(f"✅ Successfully added user {target_user_id} as an admin.")

                    # Notify the new admin
                    try:
                        # Get user's language
                        user_language = db.get_user_language(target_user_id)

                        if user_language == 'ru':
                            admin_message = "🎉 <b>Поздравляем!</b> 🎉\n\nВам предоставлены права администратора! Теперь у вас есть доступ к дополнительным функциям.\n\nИспользуйте команду /admin_commands, чтобы ознакомиться со списком доступных команд."
                        elif user_language == 'en':
                            admin_message = "🎉 <b>Congratulations!</b> 🎉\n\nYou have been given admin rights! You now have access to additional features.\n\nUse the /admin_commands command to see the list of available commands."
                        else:
                            admin_message = "🎉 <b>Gutlaýarys!</b> 🎉\n\nSize administrator hukuklary berildi! Indi has köp funksiýalara eýe bolduňyz.\n\nKomandalar sanawy bilen tanyşmak üçin /admin_commands komandasyny ulanyp bilersiňiz."

                        context.bot.send_message(
                            chat_id=target_user_id,
                            text=admin_message,
                            parse_mode=ParseMode.HTML
                        )
                    except Exception as e:
                        print(f"Error notifying new admin: {e}")
                else:
                    update.message.reply_text("❌ Failed to find ADMIN_IDS in config.py.")
            except Exception as e:
                update.message.reply_text(f"❌ Error updating config.py: {e}")

        except ValueError:
            update.message.reply_text("Invalid user ID. Please use numbers only.")
        except Exception as e:
            update.message.reply_text(f"Error adding admin: {e}")

def send_vip_welcome_message(context, user_id, days, points):
    """Send VIP welcome message to user"""
    try:
        # Get database connection
        db = Database()

        # Get user's language
        user_language = db.get_user_language(user_id)

        # Create a mock update and context for the target user
        class MockMessage:
            def __init__(self, chat_id):
                self.chat_id = chat_id

            def reply_text(self, text, parse_mode=None):
                context.bot.send_message(
                    chat_id=self.chat_id,
                    text=text,
                    parse_mode=parse_mode
                )

        class MockUser:
            def __init__(self, user_id):
                self.id = user_id

        class MockUpdate:
            def __init__(self, message, user):
                self.message = message
                self.effective_user = user

        # Create mock objects
        mock_message = MockMessage(user_id)
        mock_user = MockUser(user_id)
        mock_update = MockUpdate(mock_message, mock_user)

        # Send gift message
        if user_language == 'ru':
            gift_message = f"🎁 Вам подарено {points} поисковых баллов за покупку VIP на {days} дней!"
        elif user_language == 'en':
            gift_message = f"🎁 You have been gifted {points} search points for purchasing {days}-day VIP!"
        else:
            gift_message = f"🎁 {days} günlük VIP satyn alanyňyz üçin size {points} gözleg baly sowgat berildi!"

        # Send VIP welcome message
        if user_language == 'ru':
            welcome_message = f"🎉 <b>Поздравляем!</b> 🎉\n\nВам предоставлен VIP статус! Теперь у вас есть доступ к дополнительным функциям.\n\nВаш VIP статус будет действовать {days} дней.\n\nИспользуйте команду /commands, чтобы ознакомиться со списком доступных команд."
        elif user_language == 'en':
            welcome_message = f"🎉 <b>Congratulations!</b> 🎉\n\nYou have been given VIP status! You now have access to additional features.\n\nYour VIP status will last for {days} days.\n\nUse the /commands command to see the list of available commands."
        else:
            welcome_message = f"🎉 <b>Gutlaýarys!</b> 🎉\n\nSize VIP statusy berildi! Indi has köp funksiýalara eýe bolduňyz.\n\nVIP statusyňyz {days} gün dowam eder.\n\nKomandalar sanawy bilen tanyşmak üçin /commands komandasyny ulanyp bilersiňiz."

        context.bot.send_message(
            chat_id=user_id,
            text=welcome_message,
            parse_mode=ParseMode.HTML
        )

        # Send gift message
        context.bot.send_message(
            chat_id=user_id,
            text=gift_message,
            parse_mode=ParseMode.HTML
        )

        # Send VIP commands list
        vip_commands_command(mock_update, context)
    except Exception as e:
        print(f"Error sending VIP welcome message: {e}")
