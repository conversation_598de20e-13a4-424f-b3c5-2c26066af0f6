from telegram import Update
from telegram.ext import CallbackContext
import logging
import sys
import os

logger = logging.getLogger(__name__)

def restart_command(update: Update, context: CallbackContext):
    """Handle /restart command to fully restart the bot process"""
    user_id = update.effective_user.id
    
    # Check if user is admin
    from config import ADMIN_IDS
    if user_id not in ADMIN_IDS:
        update.message.reply_text("⛔ Bu komanda diňe administratorlar üçin elýeterlidir.")
        return
    
    # Send confirmation message
    update.message.reply_text("🔄 Bot täzeden başladylýar...")
    
    try:
        # Log the restart
        logger.info(f"Bot restart requested by user {user_id}")
        
        # Close all connections and restart the process
        from utils.db import Database
        db = Database()
        db.close()
        
        # Restart the process
        python = sys.executable
        os.execl(python, python, *sys.argv)
    except Exception as e:
        logger.error(f"Error during restart: {e}")
        update.message.reply_text(f"❌ Ýalňyşlyk ýüze çykdy: {e}")
