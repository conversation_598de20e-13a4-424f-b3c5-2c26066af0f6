from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext
from utils.constants import ParseMode
from utils.db import Database
from utils.keyboards import get_vip_keyboard
from utils.languages import get_message

def vip_command(update: Update, context: CallbackContext):
    """Handle /vip command"""
    user_id = update.effective_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Get user info
    user_info = db.get_user_info(user_id)
    is_vip = user_info.get('is_vip', False)

    # Get detailed VIP expiry information
    vip_expiry_info = db.get_vip_expiry_date(user_id)

    # Create VIP message
    if is_vip:
        # User has VIP status
        if vip_expiry_info:
            # Use the detailed VIP expiry information
            days_left = vip_expiry_info.get('days_left', 0)
            hours_left = vip_expiry_info.get('hours_left', 0)
            minutes_left = vip_expiry_info.get('minutes_left', 0)
            seconds_left = vip_expiry_info.get('seconds_left', 0)
            is_expired = vip_expiry_info.get('expired', False)

            if not is_expired:
                if language == 'ru':
                    vip_status = f"Активен. Осталось: {days_left} дней, {hours_left} часов, {minutes_left} минут, {seconds_left} секунд"
                elif language == 'en':
                    vip_status = f"Active. Time left: {days_left} days, {hours_left} hours, {minutes_left} minutes, {seconds_left} seconds"
                else:
                    vip_status = f"Işjeň. Galan wagt: {days_left} gün, {hours_left} sagat, {minutes_left} minut, {seconds_left} sekunt"
            else:
                vip_status = get_message('vip_status_expired', language)
        else:
            vip_status = get_message('vip_status_active_no_expiry', language)

        vip_text = (
            f"<b>⭐ VIP Status</b>\n\n"
            f"👑 {vip_status}\n\n"
            f"<b>VIP Artykmaçlyklary:</b>\n"
            f"✅ Çäksiz gözleg\n"
            f"✅ Köpçülikleýin gözleg\n"
            f"✅ Giňişleýin gözleg filtrleri\n"
            f"✅ Gözleg netijeleri eksporty\n"
            f"✅ Ilkinji nobatda tehniki goldaw\n"
            f"✅ Täze funksiýalara ilkinji elýeterlilik\n\n"
            f"<i>VIP statusyňyzy uzaltmak üçin, aşakdaky düwmä basyň.</i>"
        )

        # Create keyboard
        keyboard = [
            [InlineKeyboardButton("🔄 VIP Uzaltmak", callback_data="vip_buy")],
            [InlineKeyboardButton("📋 VIP Komandalar", callback_data="vip_commands")],
            [InlineKeyboardButton(get_message('back', language), callback_data="main_menu")]
        ]
    else:
        # User doesn't have VIP status
        vip_text = (
            f"<b>⭐ VIP Status</b>\n\n"
            f"👑 {get_message('vip_status_inactive', language)}\n\n"
            f"<b>VIP Artykmaçlyklary:</b>\n"
            f"✅ Çäksiz gözleg\n"
            f"✅ Köpçülikleýin gözleg\n"
            f"✅ Giňişleýin gözleg filtrleri\n"
            f"✅ Gözleg netijeleri eksporty\n"
            f"✅ Ilkinji nobatda tehniki goldaw\n"
            f"✅ Täze funksiýalara ilkinji elýeterlilik\n\n"
            f"<i>VIP statusyny satyn almak üçin, aşakdaky düwmä basyň.</i>"
        )

        # Create keyboard
        keyboard = [
            [InlineKeyboardButton("💳 VIP Satyn Almak", callback_data="vip_buy")],
            [InlineKeyboardButton(get_message('back', language), callback_data="main_menu")]
        ]

    # Send VIP message
    update.message.reply_text(
        vip_text,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )
