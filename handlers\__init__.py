# This file makes the handlers directory a Python package
# It also provides a list of available handlers for documentation purposes

__all__ = [
    # Core handlers
    'start',
    'search',
    'profile',
    'vip',
    'faq',
    'language',
    'admin',
    'history',
    'settings',
    'theme',

    # Admin handlers
    'admin_panel',
    'admin_commands',
    'admin_stats',
    'set_vip_commands',
    'set_vip_perm_command',
    'owner_commands',

    # Feature handlers
    'export_history',
    'bulk_search',
    'search_suggestions',
    'referral_manager',
    'phone_verification',
    'profile_edit',
    'search_preferences',
    'share_result',

    # Utility handlers
    'ping',
    'reload',
    'restart',
    'status',
    'support',
    'help',

    # New functionality
    'auto_stats',
    'channel_notifications',
]
