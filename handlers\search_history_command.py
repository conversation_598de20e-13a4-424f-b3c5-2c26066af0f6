"""
Search history analysis command handler
"""
import logging
from telegram import Update, ParseMode
from telegram.ext import CallbackContext
from utils.db import Database
from handlers.search_history_analysis import analyze_search_history, generate_search_report
from handlers.search_trends import get_search_trends, generate_trends_graph
from utils.languages import get_message
import os

# Set up logging
logging.basicConfig(format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO)
logger = logging.getLogger(__name__)

def search_history_analysis_command(update: Update, context: CallbackContext):
    """Handle the /search_analysis command"""
    user_id = update.effective_user.id
    db = Database()
    language = db.get_user_language(user_id)
    
    # Check if user is admin
    from config import ADMIN_IDS
    is_admin = user_id in ADMIN_IDS
    
    if not is_admin:
        update.message.reply_text(
            "Bu komanda diňe administratorlar üçin elý<PERSON>lidir.",
            parse_mode=ParseMode.HTML
        )
        return
    
    # Check if command has arguments
    args = context.args
    target_user_id = None
    days = 30
    
    if args:
        # Check if first argument is a user ID
        if args[0].isdigit():
            target_user_id = int(args[0])
        
        # Check if second argument is number of days
        if len(args) > 1 and args[1].isdigit():
            days = int(args[1])
    
    # Send initial message
    message = update.message.reply_text(
        "Gözleg taryhy analiz edilýär...",
        parse_mode=ParseMode.HTML
    )
    
    try:
        # Generate report
        report = generate_search_report(target_user_id, days)
        
        # Send report
        message.edit_text(
            report,
            parse_mode=ParseMode.MARKDOWN
        )
        
        # Generate and send trends graph
        graph_path = generate_trends_graph(days)
        if graph_path and os.path.exists(graph_path):
            with open(graph_path, 'rb') as photo:
                update.message.reply_photo(
                    photo=photo,
                    caption=f"Iň köp gözlenenler (soňky {days} gün)",
                    parse_mode=ParseMode.HTML
                )
    
    except Exception as e:
        logger.error(f"Error in search history analysis command: {e}")
        message.edit_text(
            f"Gözleg taryhy analiz edilende ýalňyşlyk ýüze çykdy: {str(e)}",
            parse_mode=ParseMode.HTML
        )

def search_trends_command(update: Update, context: CallbackContext):
    """Handle the /search_trends command"""
    user_id = update.effective_user.id
    db = Database()
    language = db.get_user_language(user_id)
    
    # Check if user is admin
    from config import ADMIN_IDS
    is_admin = user_id in ADMIN_IDS
    
    if not is_admin:
        update.message.reply_text(
            "Bu komanda diňe administratorlar üçin elýeterlidir.",
            parse_mode=ParseMode.HTML
        )
        return
    
    # Check if command has arguments
    args = context.args
    days = 30
    
    if args and args[0].isdigit():
        days = int(args[0])
    
    # Send initial message
    message = update.message.reply_text(
        "Gözleg trendleri analiz edilýär...",
        parse_mode=ParseMode.HTML
    )
    
    try:
        # Get trends
        trends = get_search_trends(days)
        
        if not trends:
            message.edit_text(
                "Gözleg trendleri tapylmady.",
                parse_mode=ParseMode.HTML
            )
            return
        
        # Create message
        text = f"🔍 <b>Iň köp gözlenenler (soňky {days} gün)</b>\n\n"
        
        for i, item in enumerate(trends, 1):
            text += f"{i}. {item['query']} - {item['count']} gezek\n"
        
        # Send message
        message.edit_text(
            text,
            parse_mode=ParseMode.HTML
        )
        
        # Generate and send trends graph
        graph_path = generate_trends_graph(days)
        if graph_path and os.path.exists(graph_path):
            with open(graph_path, 'rb') as photo:
                update.message.reply_photo(
                    photo=photo,
                    caption=f"Iň köp gözlenenler (soňky {days} gün)",
                    parse_mode=ParseMode.HTML
                )
    
    except Exception as e:
        logger.error(f"Error in search trends command: {e}")
        message.edit_text(
            f"Gözleg trendleri analiz edilende ýalňyşlyk ýüze çykdy: {str(e)}",
            parse_mode=ParseMode.HTML
        )