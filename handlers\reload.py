from telegram import Update
from telegram.ext import CallbackContext
from utils.db import Database
import logging

logger = logging.getLogger(__name__)

def reload_command(update: Update, context: CallbackContext):
    """Handle /reload command to restart the bot connection"""
    user_id = update.effective_user.id
    
    # Get user's language
    db = Database()
    
    # Check if user is admin
    from config import ADMIN_IDS
    if user_id not in ADMIN_IDS:
        update.message.reply_text("⛔ Bu komanda diňe administratorlar üçin elýeterlidir.")
        return
    
    # Send confirmation message
    update.message.reply_text("🔄 Bot baglanyşygy täzeden başladylýar...")
    
    try:
        # Close database connections
        db.close()
        
        # Log the reload
        logger.info(f"Bot connection reload requested by user {user_id}")
        
        # Force updater to reconnect by stopping and starting polling
        # This needs to be done in the main thread, so we'll just notify
        context.bot.send_message(
            chat_id=user_id,
            text="✅ Baglanyşyk täzeden başladyldy!"
        )
    except Exception as e:
        logger.error(f"Error during reload: {e}")
        update.message.reply_text(f"❌ Ýalňyşlyk ýüze çykdy: {e}")
