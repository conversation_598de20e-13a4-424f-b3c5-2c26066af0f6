import os
import json
from datetime import datetime
# Import telegram modules with try/except to avoid IDE warnings
try:
    from telegram import Update, ReplyKeyboardMarkup, KeyboardButton, Reply<PERSON>eyboardRemove
    from utils.constants import ParseMode
    from telegram.ext import CallbackContext
except ImportError:
    # Define placeholder classes for type hints
    class Update: pass
    class CallbackContext: pass
    class ReplyKeyboardMarkup: pass
    class KeyboardButton: pass
    class ReplyKeyboardRemove: pass
    class ParseMode: pass

from utils.db import Database
from utils.languages import get_message
from utils.keyboards import get_phone_request_keyboard, get_main_menu_keyboard

# Directory to store user phone data
USER_PHONES_DIR = "user_phones"

# Ensure directory exists
os.makedirs(USER_PHONES_DIR, exist_ok=True)

def request_phone_verification(update: Update, context: CallbackContext):
    """Request phone verification from user"""
    user_id = update.effective_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Send phone verification request with clear instructions
    message = get_message('phone_confirm', language)

    # Add more detailed instructions based on language
    if language == 'ru':
        message += "\n\n⚠️ <b>Важно:</b> Для использования бота необходимо подтвердить номер телефона. Нажмите кнопку ниже, чтобы поделиться своим номером."
    elif language == 'en':
        message += "\n\n⚠️ <b>Important:</b> You need to verify your phone number to use the bot. Press the button below to share your number."
    else:  # Default to Turkmen
        message += "\n\n⚠️ <b>Möhüm:</b> Boty ulanmak üçin telefon belgiňizi tassyklamagyňyz zerur. Belgiňizi paýlaşmak üçin aşakdaky düwmä basyň."

    update.message.reply_text(
        message,
        reply_markup=get_phone_request_keyboard(language),
        parse_mode=ParseMode.HTML
    )

    # Set flag to expect phone number
    context.user_data['awaiting_phone_verification'] = True

    return True

def handle_contact_message(update: Update, context: CallbackContext):
    """Handle contact message with phone number"""
    user_id = update.effective_user.id
    user = update.effective_user

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Check if we're expecting phone verification
    if not context.user_data.get('awaiting_phone_verification', False):
        return False

    # Clear the flag
    context.user_data['awaiting_phone_verification'] = False

    # Get phone number from contact
    contact = update.message.contact
    if contact and contact.phone_number:
        phone_number = contact.phone_number

        # Remove any spaces or special characters
        phone_number = ''.join(filter(str.isdigit, phone_number))

        # Update user's phone number in database
        db.update_user_phone(user_id, phone_number)

        # Save user's phone data to file
        save_user_phone_data(user_id, user.username, user.full_name, phone_number)

        # Send success message with appropriate text based on language
        if language == 'ru':
            success_message = get_message('phone_success', language) + "\n\n✅ <b>Теперь вы можете использовать все функции бота!</b>"
        elif language == 'en':
            success_message = get_message('phone_success', language) + "\n\n✅ <b>Now you can use all the bot's features!</b>"
        else:  # Default to Turkmen
            success_message = get_message('phone_success', language) + "\n\n✅ <b>Indi botuň ähli funksiýalaryny ulanyp bilersiňiz!</b>"

        update.message.reply_text(
            success_message,
            reply_markup=ReplyKeyboardRemove(),
            parse_mode=ParseMode.HTML
        )

        # Show main menu with welcome message
        welcome_text = get_message('welcome', language)

        # Add notification info if there are unread notifications
        unread_count = db.get_unread_notifications_count(user_id)
        if unread_count > 0:
            if language == 'ru':
                notification_text = f"\n\n📬 У вас есть {unread_count} непрочитанных уведомлений!"
            elif language == 'en':
                notification_text = f"\n\n📬 You have {unread_count} unread notifications!"
            else:
                notification_text = f"\n\n📬 Siziň {unread_count} sany okalmedik bildirişiňiz bar!"
            welcome_text += notification_text

        update.message.reply_text(
            welcome_text,
            reply_markup=get_main_menu_keyboard(language, user_id),
            parse_mode=ParseMode.HTML
        )

        return True

    # If no valid phone number was provided
    update.message.reply_text(
        get_message('phone_verification_failed', language),
        reply_markup=ReplyKeyboardRemove(),
        parse_mode=ParseMode.HTML
    )

    # Try again with phone verification request
    request_phone_verification(update, context)

    return False

def save_user_phone_data(user_id, username, full_name, phone_number):
    """Save user's phone data to a file and database"""
    # Create user data dictionary
    user_data = {
        'user_id': user_id,
        'username': username or "",
        'full_name': full_name or "",
        'phone_number': phone_number,
        'verified_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }

    # Create filename with user_id
    filename = os.path.join(USER_PHONES_DIR, f"user_{user_id}.json")

    # Save to file
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(user_data, f, ensure_ascii=False, indent=4)

    # Also save to database
    db = Database()
    db.update_user_phone(user_id, phone_number)

    return True

def handle_phone_verification_callback(update: Update, context: CallbackContext):
    """Handle callback query for phone verification"""
    query = update.callback_query
    user_id = update.effective_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Answer the callback query to stop the loading animation
    query.answer()

    # Send phone verification request with keyboard
    query.message.reply_text(
        get_message('phone_confirm', language),
        reply_markup=get_phone_request_keyboard(language),
        parse_mode=ParseMode.HTML
    )

    # Set flag to expect phone number
    context.user_data['awaiting_phone_verification'] = True

    return True

def is_phone_verified(user_id):
    """Check if user's phone is verified"""
    # First check in database
    db = Database()
    if db.is_phone_verified(user_id):
        return True

    # Then check if file exists as fallback
    filename = os.path.join(USER_PHONES_DIR, f"user_{user_id}.json")
    return os.path.exists(filename)
