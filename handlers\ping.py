from telegram import Update
from telegram.ext import CallbackContext
import time
import datetime

def ping_command(update: Update, context: CallbackContext):
    """Handle /ping command to check bot responsiveness"""
    start_time = time.time()
    
    # Get current time
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Send initial message
    message = update.message.reply_text("Pinging...")
    
    # Calculate response time
    end_time = time.time()
    response_time = round((end_time - start_time) * 1000, 2)
    
    # Edit message with ping results
    message.edit_text(
        f"🏓 Pong!\n\n"
        f"⏱️ Response time: {response_time}ms\n"
        f"🕒 Server time: {current_time}\n\n"
        f"✅ Bot is online and responding!"
    )
