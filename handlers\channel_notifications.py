"""
Channel notifications module for automatically posting updates to a Telegram channel
"""
import logging
from telegram import Bo<PERSON>, ParseMode
from utils.db import Database
import datetime
import json
import os

# Set up logging
logging.basicConfig(format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO)
logger = logging.getLogger(__name__)

# Load channel ID from config
try:
    from config import CHANNEL_ID, TOKEN
    NOTIFICATIONS_ENABLED = True
except (ImportError, AttributeError):
    logger.warning("Channel ID not found in config. Channel notifications will be disabled.")
    NOTIFICATIONS_ENABLED = False

def send_channel_message(message, bot=None):
    """Send a message to the channel"""
    if not NOTIFICATIONS_ENABLED:
        logger.warning("Channel notifications are disabled.")
        return False
    
    try:
        # If bot is not provided, create a new one
        if bot is None:
            bot = Bot(TOKEN)
        
        # Send message to channel
        bot.send_message(
            chat_id=CHANNEL_ID,
            text=message,
            parse_mode=ParseMode.HTML
        )
        
        logger.info(f"Message sent to channel: {message[:50]}...")
        return True
    except Exception as e:
        logger.error(f"Error sending message to channel: {e}")
        return False

def notify_new_users(context=None):
    """Send notification about new users to the channel"""
    if not NOTIFICATIONS_ENABLED:
        return False
    
    try:
        # Get database instance
        db = Database()
        
        # Get users who joined in the last 24 hours
        new_users = db.get_new_users(days=1)
        
        # If no new users, don't send notification
        if not new_users:
            return False
        
        # Create message
        message = f"🆕 <b>Täze ulanyjylar ({len(new_users)})</b>\n\n"
        
        # Add user information
        for i, user in enumerate(new_users[:10], 1):  # Show only first 10 users
            username = user.get('username', '')
            if username and not username.startswith('@'):
                username = f"@{username}"
            
            message += f"{i}. {username or 'Anonim'} - {user.get('joined_date', '')}\n"
        
        # Add "and more" if there are more users
        if len(new_users) > 10:
            message += f"\nWe {len(new_users) - 10} sany has-da täze ulanyjy..."
        
        # Send message to channel
        bot = context.bot if context else None
        send_channel_message(message, bot)
        
        return True
    except Exception as e:
        logger.error(f"Error notifying about new users: {e}")
        return False

def notify_new_vip_users(context=None):
    """Send notification about new VIP users to the channel"""
    if not NOTIFICATIONS_ENABLED:
        return False
    
    try:
        # Get database instance
        db = Database()
        
        # Get VIP status changes in the last 24 hours
        yesterday = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
        
        db.cur.execute("""
            SELECT user_id, username, full_name, vip_expiry_date
            FROM users
            WHERE is_vip = 1 AND vip_status_changed_at >= ?
        """, (yesterday,))
        
        new_vip_users = db.cur.fetchall()
        
        # If no new VIP users, don't send notification
        if not new_vip_users:
            return False
        
        # Create message
        message = f"⭐ <b>Täze VIP ulanyjylar ({len(new_vip_users)})</b>\n\n"
        
        # Add user information
        for i, (user_id, username, full_name, vip_expiry_date) in enumerate(new_vip_users[:10], 1):
            if username and not username.startswith('@'):
                username = f"@{username}"
            
            # Calculate VIP duration
            if vip_expiry_date:
                try:
                    expiry_date = datetime.datetime.strptime(vip_expiry_date, '%Y-%m-%d %H:%M:%S')
                    current_date = datetime.datetime.now()
                    days_left = (expiry_date - current_date).days
                    
                    if days_left > 1000:  # Permanent VIP
                        vip_info = "Hemişelik"
                    else:
                        vip_info = f"{days_left} gün"
                except:
                    vip_info = "Näbelli"
            else:
                vip_info = "Näbelli"
            
            message += f"{i}. {username or full_name or 'Anonim'} - {vip_info}\n"
        
        # Add "and more" if there are more users
        if len(new_vip_users) > 10:
            message += f"\nWe {len(new_vip_users) - 10} sany has-da täze VIP ulanyjy..."
        
        # Send message to channel
        bot = context.bot if context else None
        send_channel_message(message, bot)
        
        return True
    except Exception as e:
        logger.error(f"Error notifying about new VIP users: {e}")
        return False

def notify_daily_stats(context=None):
    """Send daily statistics to the channel"""
    if not NOTIFICATIONS_ENABLED:
        return False
    
    try:
        # Get database instance
        db = Database()
        
        # Get current date
        current_date = datetime.datetime.now().strftime('%Y-%m-%d')
        
        # Get statistics
        total_users = db.get_total_users_count()
        vip_users = db.get_vip_users_count()
        daily_searches = db.get_daily_searches_count()
        new_users_today = len(db.get_new_users(days=1))
        
        # Create message
        message = f"📊 <b>Gündelik statistika ({current_date})</b>\n\n"
        message += f"👥 Jemi ulanyjylar: {total_users}\n"
        message += f"⭐ VIP ulanyjylar: {vip_users} ({vip_users/total_users*100:.1f}%)\n"
        message += f"🔍 Şu günki gözlegler: {daily_searches}\n"
        message += f"🆕 Şu günki täze ulanyjylar: {new_users_today}\n"
        
        # Get top searches
        top_searches = db.get_most_searched_queries(limit=5)
        if top_searches:
            message += f"\n🔝 <b>Iň köp gözlenenler:</b>\n"
            for i, (query, count) in enumerate(top_searches, 1):
                message += f"{i}. {query} - {count} gezek\n"
        
        # Send message to channel
        bot = context.bot if context else None
        send_channel_message(message, bot)
        
        return True
    except Exception as e:
        logger.error(f"Error notifying about daily stats: {e}")
        return False

def setup_channel_notification_jobs(job_queue):
    """Set up jobs for channel notifications"""
    if not NOTIFICATIONS_ENABLED:
        logger.warning("Channel notifications are disabled. Jobs will not be scheduled.")
        return
    
    # Schedule daily stats notification at 23:55
    job_queue.run_daily(
        notify_daily_stats,
        datetime.time(hour=23, minute=55, second=0)
    )
    logger.info("Daily stats notification job scheduled")
    
    # Schedule new users notification at 12:00
    job_queue.run_daily(
        notify_new_users,
        datetime.time(hour=12, minute=0, second=0)
    )
    logger.info("New users notification job scheduled")
    
    # Schedule new VIP users notification at 18:00
    job_queue.run_daily(
        notify_new_vip_users,
        datetime.time(hour=18, minute=0, second=0)
    )
    logger.info("New VIP users notification job scheduled")
