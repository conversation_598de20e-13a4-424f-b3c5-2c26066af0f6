"""
Handler for search statistics
"""
# Import telegram modules with try/except to avoid IDE warnings
try:
    from telegram import Update
    from telegram.ext import CallbackContext
    from utils.constants import ParseMode
except ImportError:
    # Define placeholder classes for type hints
    class Update: pass
    class CallbackContext: pass
    class ParseMode:
        HTML = "HTML"

from utils.db import Database
from utils.search_storage import get_search_statistics, get_all_searches
from utils.keyboards import get_main_menu_keyboard

def search_stats_command(update: Update, context: CallbackContext):
    """Handle the /search_stats command - show search statistics"""
    user_id = update.effective_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Check if user is admin
    from config import ADMIN_IDS
    if user_id not in ADMIN_IDS:
        # User is not admin, send error message
        if language == 'ru':
            error_message = "⛔ <b>У вас нет доступа к этой команде.</b>\n\nЭта команда доступна только администраторам."
        elif language == 'en':
            error_message = "⛔ <b>You don't have access to this command.</b>\n\nThis command is only available to administrators."
        else:
            error_message = "⛔ <b>Bu buýruga girip bilmersiňiz.</b>\n\nBu buýruk diňe administratorlar üçin elýeterlidir."

        update.message.reply_text(
            error_message,
            parse_mode=ParseMode.HTML
        )
        return

    # Get search statistics
    stats = get_search_statistics()

    # Format statistics message
    if language == 'ru':
        stats_message = f"""📊 <b>Статистика поисков</b>

🔍 Всего поисков: {stats['total_searches']}
👥 Уникальных пользователей: {stats['unique_users']}
📱 Поиски по номеру телефона: {stats['phone_searches']}
👤 Поиски по имени: {stats['name_searches']}
✅ Поиски с результатами: {stats['searches_with_results']}
❌ Поиски без результатов: {stats['searches_without_results']}
"""
    elif language == 'en':
        stats_message = f"""📊 <b>Search Statistics</b>

🔍 Total searches: {stats['total_searches']}
👥 Unique users: {stats['unique_users']}
📱 Phone number searches: {stats['phone_searches']}
👤 Name searches: {stats['name_searches']}
✅ Searches with results: {stats['searches_with_results']}
❌ Searches without results: {stats['searches_without_results']}
"""
    else:
        stats_message = f"""📊 <b>Gözleg statistikasy</b>

🔍 Jemi gözlegler: {stats['total_searches']}
👥 Dürli ulanyjylar: {stats['unique_users']}
📱 Telefon belgisi gözlegleri: {stats['phone_searches']}
👤 At boýunça gözlegler: {stats['name_searches']}
✅ Netijeli gözlegler: {stats['searches_with_results']}
❌ Netijesiz gözlegler: {stats['searches_without_results']}
"""

    # Send statistics message
    update.message.reply_text(
        stats_message,
        parse_mode=ParseMode.HTML
    )

    # Get recent searches (limit to 5)
    recent_searches = get_all_searches()[:5]

    # Format recent searches message
    if language == 'ru':
        recent_message = "<b>Последние 5 поисков:</b>\n\n"
    elif language == 'en':
        recent_message = "<b>Last 5 searches:</b>\n\n"
    else:
        recent_message = "<b>Soňky 5 gözleg:</b>\n\n"

    for i, search in enumerate(recent_searches, 1):
        user_info = search.get('user_info', {})
        search_info = search.get('search_info', {})
        results = search.get('results', [])

        username = user_info.get('username', '')
        full_name = user_info.get('full_name', '')
        query = search_info.get('query', '')
        timestamp = search_info.get('timestamp', '')

        if language == 'ru':
            recent_message += f"{i}. <b>Пользователь:</b> {full_name} (@{username})\n"
            recent_message += f"   <b>Запрос:</b> {query}\n"
            recent_message += f"   <b>Время:</b> {timestamp}\n"
            recent_message += f"   <b>Результаты:</b> {len(results)}\n\n"
        elif language == 'en':
            recent_message += f"{i}. <b>User:</b> {full_name} (@{username})\n"
            recent_message += f"   <b>Query:</b> {query}\n"
            recent_message += f"   <b>Time:</b> {timestamp}\n"
            recent_message += f"   <b>Results:</b> {len(results)}\n\n"
        else:
            recent_message += f"{i}. <b>Ulanyjy:</b> {full_name} (@{username})\n"
            recent_message += f"   <b>Gözleg:</b> {query}\n"
            recent_message += f"   <b>Wagty:</b> {timestamp}\n"
            recent_message += f"   <b>Netijeler:</b> {len(results)}\n\n"

    # Send recent searches message
    update.message.reply_text(
        recent_message,
        parse_mode=ParseMode.HTML
    )

    return True
