from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext
from utils.constants import ParseMode
from utils.db import Database
from utils.languages import get_message

def search_preferences_callback(update: Update, context: CallbackContext):
    """Handle search preferences callback"""
    query = update.callback_query
    user_id = query.from_user.id
    
    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)
    
    # Get user's search preferences
    preferences = db.get_search_preferences(user_id)
    
    # Create search preferences message
    if language == 'ru':
        prefs_text = "<b>🔍 Настройки поиска</b>\n\n"
        prefs_text += f"Результатов на страницу: <b>{preferences['results_per_page']}</b>\n"
        prefs_text += f"Порядок сортировки: <b>{'По убыванию' if preferences['sort_order'] == 'desc' else 'По возрастанию'}</b>\n"
        prefs_text += f"Тип поиска по умолчанию: <b>{'Телефон' if preferences['default_search_type'] == 'phone' else 'Имя' if preferences['default_search_type'] == 'name' else 'Паспорт'}</b>"
    elif language == 'en':
        prefs_text = "<b>🔍 Search Settings</b>\n\n"
        prefs_text += f"Results per page: <b>{preferences['results_per_page']}</b>\n"
        prefs_text += f"Sort order: <b>{'Descending' if preferences['sort_order'] == 'desc' else 'Ascending'}</b>\n"
        prefs_text += f"Default search type: <b>{'Phone' if preferences['default_search_type'] == 'phone' else 'Name' if preferences['default_search_type'] == 'name' else 'Passport'}</b>"
    else:
        prefs_text = "<b>🔍 Gözleg sazlamalary</b>\n\n"
        prefs_text += f"Sahypadaky netijeler: <b>{preferences['results_per_page']}</b>\n"
        prefs_text += f"Tertip: <b>{'Kemeleýän' if preferences['sort_order'] == 'desc' else 'Köpeleýän'}</b>\n"
        prefs_text += f"Adaty gözleg görnüşi: <b>{'Telefon' if preferences['default_search_type'] == 'phone' else 'Ady' if preferences['default_search_type'] == 'name' else 'Pasport'}</b>"
    
    # Create keyboard
    if language == 'ru':
        keyboard = [
            [InlineKeyboardButton("📊 Результатов на страницу", callback_data="set_results_per_page")],
            [InlineKeyboardButton("🔄 Порядок сортировки", callback_data="set_sort_order")],
            [InlineKeyboardButton("🔍 Тип поиска по умолчанию", callback_data="set_default_type")],
            [InlineKeyboardButton("🔙 Назад", callback_data="profile_settings")]
        ]
    elif language == 'en':
        keyboard = [
            [InlineKeyboardButton("📊 Results per page", callback_data="set_results_per_page")],
            [InlineKeyboardButton("🔄 Sort order", callback_data="set_sort_order")],
            [InlineKeyboardButton("🔍 Default search type", callback_data="set_default_type")],
            [InlineKeyboardButton("🔙 Back", callback_data="profile_settings")]
        ]
    else:
        keyboard = [
            [InlineKeyboardButton("📊 Sahypadaky netijeler", callback_data="set_results_per_page")],
            [InlineKeyboardButton("🔄 Tertip", callback_data="set_sort_order")],
            [InlineKeyboardButton("🔍 Adaty gözleg görnüşi", callback_data="set_default_type")],
            [InlineKeyboardButton("🔙 Yza", callback_data="profile_settings")]
        ]
    
    # Send search preferences message
    query.edit_message_text(
        prefs_text,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def set_results_per_page_callback(update: Update, context: CallbackContext):
    """Handle set results per page callback"""
    query = update.callback_query
    user_id = query.from_user.id
    
    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)
    
    # Get current setting
    preferences = db.get_search_preferences(user_id)
    current_setting = preferences['results_per_page']
    
    # Create message
    if language == 'ru':
        message = f"<b>📊 Результатов на страницу</b>\n\n"
        message += f"Текущее значение: <b>{current_setting}</b>\n\n"
        message += "Выберите количество результатов для отображения на одной странице:"
    elif language == 'en':
        message = f"<b>📊 Results per page</b>\n\n"
        message += f"Current value: <b>{current_setting}</b>\n\n"
        message += "Choose how many results to display on one page:"
    else:
        message = f"<b>📊 Sahypadaky netijeler</b>\n\n"
        message += f"Häzirki baha: <b>{current_setting}</b>\n\n"
        message += "Bir sahypada näçe netije görkeziljekdigini saýlaň:"
    
    # Create keyboard
    keyboard = [
        [
            InlineKeyboardButton("3", callback_data="results_per_page_3"),
            InlineKeyboardButton("5", callback_data="results_per_page_5"),
            InlineKeyboardButton("10", callback_data="results_per_page_10")
        ],
        [
            InlineKeyboardButton("15", callback_data="results_per_page_15"),
            InlineKeyboardButton("20", callback_data="results_per_page_20"),
            InlineKeyboardButton("25", callback_data="results_per_page_25")
        ]
    ]
    
    # Add back button
    if language == 'ru':
        keyboard.append([InlineKeyboardButton("🔙 Назад", callback_data="search_preferences")])
    elif language == 'en':
        keyboard.append([InlineKeyboardButton("🔙 Back", callback_data="search_preferences")])
    else:
        keyboard.append([InlineKeyboardButton("🔙 Yza", callback_data="search_preferences")])
    
    # Send message
    query.edit_message_text(
        message,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def handle_results_per_page(update: Update, context: CallbackContext):
    """Handle results per page selection"""
    query = update.callback_query
    user_id = query.from_user.id
    
    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)
    
    # Get selected value
    selected_value = int(query.data.split('_')[-1])
    
    # Save preference
    db.save_search_preferences(user_id, results_per_page=selected_value)
    
    # Confirmation message
    if language == 'ru':
        confirmation = f"✅ Количество результатов на страницу установлено на {selected_value}."
    elif language == 'en':
        confirmation = f"✅ Results per page set to {selected_value}."
    else:
        confirmation = f"✅ Sahypadaky netijeler {selected_value} edip bellendi."
    
    query.answer(confirmation)
    
    # Return to search preferences
    query.data = "search_preferences"
    search_preferences_callback(update, context)

def set_sort_order_callback(update: Update, context: CallbackContext):
    """Handle set sort order callback"""
    query = update.callback_query
    user_id = query.from_user.id
    
    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)
    
    # Get current setting
    preferences = db.get_search_preferences(user_id)
    current_setting = preferences['sort_order']
    
    # Create message
    if language == 'ru':
        message = f"<b>🔄 Порядок сортировки</b>\n\n"
        message += f"Текущее значение: <b>{'По убыванию' if current_setting == 'desc' else 'По возрастанию'}</b>\n\n"
        message += "Выберите порядок сортировки результатов:"
    elif language == 'en':
        message = f"<b>🔄 Sort order</b>\n\n"
        message += f"Current value: <b>{'Descending' if current_setting == 'desc' else 'Ascending'}</b>\n\n"
        message += "Choose the sort order for results:"
    else:
        message = f"<b>🔄 Tertip</b>\n\n"
        message += f"Häzirki baha: <b>{'Kemeleýän' if current_setting == 'desc' else 'Köpeleýän'}</b>\n\n"
        message += "Netijeleriň tertibini saýlaň:"
    
    # Create keyboard
    if language == 'ru':
        keyboard = [
            [InlineKeyboardButton("⬆️ По возрастанию", callback_data="sort_order_asc")],
            [InlineKeyboardButton("⬇️ По убыванию", callback_data="sort_order_desc")],
            [InlineKeyboardButton("🔙 Назад", callback_data="search_preferences")]
        ]
    elif language == 'en':
        keyboard = [
            [InlineKeyboardButton("⬆️ Ascending", callback_data="sort_order_asc")],
            [InlineKeyboardButton("⬇️ Descending", callback_data="sort_order_desc")],
            [InlineKeyboardButton("🔙 Back", callback_data="search_preferences")]
        ]
    else:
        keyboard = [
            [InlineKeyboardButton("⬆️ Köpeleýän", callback_data="sort_order_asc")],
            [InlineKeyboardButton("⬇️ Kemeleýän", callback_data="sort_order_desc")],
            [InlineKeyboardButton("🔙 Yza", callback_data="search_preferences")]
        ]
    
    # Send message
    query.edit_message_text(
        message,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def handle_sort_order(update: Update, context: CallbackContext):
    """Handle sort order selection"""
    query = update.callback_query
    user_id = query.from_user.id
    
    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)
    
    # Get selected value
    selected_value = query.data.split('_')[-1]
    
    # Save preference
    db.save_search_preferences(user_id, sort_order=selected_value)
    
    # Confirmation message
    if language == 'ru':
        confirmation = f"✅ Порядок сортировки установлен на {'По убыванию' if selected_value == 'desc' else 'По возрастанию'}."
    elif language == 'en':
        confirmation = f"✅ Sort order set to {'Descending' if selected_value == 'desc' else 'Ascending'}."
    else:
        confirmation = f"✅ Tertip {'Kemeleýän' if selected_value == 'desc' else 'Köpeleýän'} edip bellendi."
    
    query.answer(confirmation)
    
    # Return to search preferences
    query.data = "search_preferences"
    search_preferences_callback(update, context)

def set_default_type_callback(update: Update, context: CallbackContext):
    """Handle set default search type callback"""
    query = update.callback_query
    user_id = query.from_user.id
    
    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)
    
    # Get current setting
    preferences = db.get_search_preferences(user_id)
    current_setting = preferences['default_search_type']
    
    # Create message
    if language == 'ru':
        message = f"<b>🔍 Тип поиска по умолчанию</b>\n\n"
        message += f"Текущее значение: <b>{'Телефон' if current_setting == 'phone' else 'Имя' if current_setting == 'name' else 'Паспорт'}</b>\n\n"
        message += "Выберите тип поиска, который будет использоваться по умолчанию:"
    elif language == 'en':
        message = f"<b>🔍 Default search type</b>\n\n"
        message += f"Current value: <b>{'Phone' if current_setting == 'phone' else 'Name' if current_setting == 'name' else 'Passport'}</b>\n\n"
        message += "Choose the search type that will be used by default:"
    else:
        message = f"<b>🔍 Adaty gözleg görnüşi</b>\n\n"
        message += f"Häzirki baha: <b>{'Telefon' if current_setting == 'phone' else 'Ady' if current_setting == 'name' else 'Pasport'}</b>\n\n"
        message += "Adaty ulanylýan gözleg görnüşini saýlaň:"
    
    # Create keyboard
    if language == 'ru':
        keyboard = [
            [InlineKeyboardButton("📱 Телефон", callback_data="default_type_phone")],
            [InlineKeyboardButton("👤 Имя", callback_data="default_type_name")],
            [InlineKeyboardButton("🆔 Паспорт", callback_data="default_type_passport")],
            [InlineKeyboardButton("🔙 Назад", callback_data="search_preferences")]
        ]
    elif language == 'en':
        keyboard = [
            [InlineKeyboardButton("📱 Phone", callback_data="default_type_phone")],
            [InlineKeyboardButton("👤 Name", callback_data="default_type_name")],
            [InlineKeyboardButton("🆔 Passport", callback_data="default_type_passport")],
            [InlineKeyboardButton("🔙 Back", callback_data="search_preferences")]
        ]
    else:
        keyboard = [
            [InlineKeyboardButton("📱 Telefon", callback_data="default_type_phone")],
            [InlineKeyboardButton("👤 Ady", callback_data="default_type_name")],
            [InlineKeyboardButton("🆔 Pasport", callback_data="default_type_passport")],
            [InlineKeyboardButton("🔙 Yza", callback_data="search_preferences")]
        ]
    
    # Send message
    query.edit_message_text(
        message,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def handle_default_type(update: Update, context: CallbackContext):
    """Handle default search type selection"""
    query = update.callback_query
    user_id = query.from_user.id
    
    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)
    
    # Get selected value
    selected_value = query.data.split('_')[-1]
    
    # Save preference
    db.save_search_preferences(user_id, default_search_type=selected_value)
    
    # Confirmation message
    if language == 'ru':
        type_name = 'Телефон' if selected_value == 'phone' else 'Имя' if selected_value == 'name' else 'Паспорт'
        confirmation = f"✅ Тип поиска по умолчанию установлен на {type_name}."
    elif language == 'en':
        type_name = 'Phone' if selected_value == 'phone' else 'Name' if selected_value == 'name' else 'Passport'
        confirmation = f"✅ Default search type set to {type_name}."
    else:
        type_name = 'Telefon' if selected_value == 'phone' else 'Ady' if selected_value == 'name' else 'Pasport'
        confirmation = f"✅ Adaty gözleg görnüşi {type_name} edip bellendi."
    
    query.answer(confirmation)
    
    # Return to search preferences
    query.data = "search_preferences"
    search_preferences_callback(update, context)
