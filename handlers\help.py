from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from utils.constants import ParseMode
from telegram.ext import CallbackContext
from utils.db import Database
from utils.languages import get_message

def help_command(update: Update, context: CallbackContext):
    """Handle /help command"""
    user_id = update.effective_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Create help message
    if language == 'ru':
        help_text = (
            "<b>📚 Справка по командам бота</b>\n\n"
            "🔍 <b>Поиск:</b>\n"
            "/search - Начать поиск\n"
            "/bulk - Массовый поиск (только для VIP)\n\n"

            "👤 <b>Профиль:</b>\n"
            "/profile - Просмотр профиля\n"
            "/settings - Настройки\n"
            "/language - Изменить язык\n\n"

            "📖 <b>История и избранное:</b>\n"
            "/history - История поиска\n"
            "/favorites - Избранные результаты\n"
            "/export - Экспорт истории\n\n"

            "⭐ <b>VIP:</b>\n"
            "/vip - Информация о VIP\n"
            "/promo - Использовать промокод\n\n"

            "ℹ️ <b>Информация:</b>\n"
            "/help - Эта справка\n"
            "/commands - Полный список команд\n"
            "/faq - Часто задаваемые вопросы\n"
            "/support - Техническая поддержка\n\n"

            "🔔 <b>Уведомления:</b>\n"
            "/notifications - Просмотр уведомлений\n\n"

            "🛠 <b>Системные:</b>\n"
            "/ping - Проверка работы бота\n"
            "/status - Статус бота\n"
        )
    elif language == 'en':
        help_text = (
            "<b>📚 Bot Commands Help</b>\n\n"
            "🔍 <b>Search:</b>\n"
            "/search - Start search\n"
            "/bulk - Bulk search (VIP only)\n\n"

            "👤 <b>Profile:</b>\n"
            "/profile - View profile\n"
            "/settings - Settings\n"
            "/language - Change language\n\n"

            "📖 <b>History and Favorites:</b>\n"
            "/history - Search history\n"
            "/favorites - Favorite results\n"
            "/export - Export history\n\n"

            "⭐ <b>VIP:</b>\n"
            "/vip - VIP information\n"
            "/promo - Use promo code\n\n"

            "ℹ️ <b>Information:</b>\n"
            "/help - This help\n"
            "/commands - Full command list\n"
            "/faq - Frequently asked questions\n"
            "/support - Technical support\n\n"

            "🔔 <b>Notifications:</b>\n"
            "/notifications - View notifications\n\n"

            "🛠 <b>System:</b>\n"
            "/ping - Check bot operation\n"
            "/status - Bot status\n"
        )
    else:  # Default to Turkmen
        help_text = (
            "<b>📚 Bot Komandalary Kömegi</b>\n\n"
            "🔍 <b>Gözleg:</b>\n"
            "/search - Gözlege başlamak\n"
            "/bulk - Köpçülikleýin gözleg (diňe VIP)\n\n"

            "👤 <b>Profil:</b>\n"
            "/profile - Profili görmek\n"
            "/settings - Sazlamalar\n"
            "/language - Dili üýtgetmek\n\n"

            "📖 <b>Taryh we Bellikler:</b>\n"
            "/history - Gözleg taryhy\n"
            "/favorites - Saýlanan netijeler\n"
            "/export - Taryhy eksport etmek\n\n"

            "⭐ <b>VIP:</b>\n"
            "/vip - VIP barada maglumat\n"
            "/promo - Promo kod ulanmak\n\n"

            "ℹ️ <b>Maglumat:</b>\n"
            "/help - Bu kömek\n"
            "/commands - Doly komandalar sanawy\n"
            "/faq - Köp soralýan soraglar\n"
            "/support - Tehniki goldaw\n\n"

            "🔔 <b>Bildirişler:</b>\n"
            "/notifications - Bildirişleri görmek\n\n"

            "🛠 <b>Ulgam:</b>\n"
            "/ping - Botuň işleýşini barlamak\n"
            "/status - Bot statusy\n"
        )

    # Create keyboard
    keyboard = [
        [InlineKeyboardButton(get_message('commands_button', language), callback_data="user_commands")],
        [InlineKeyboardButton(get_message('faq_button', language), callback_data="faq_view")],
        [InlineKeyboardButton(get_message('support_button', language), callback_data="support")]
    ]

    # Send help message
    update.message.reply_text(
        help_text,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def help_callback(update: Update, context: CallbackContext):
    """Handle help callback query"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Create help message
    if language == 'ru':
        help_text = (
            "<b>📚 Справка по командам бота</b>\n\n"
            "🔍 <b>Поиск:</b>\n"
            "/search - Начать поиск\n"
            "/bulk - Массовый поиск (только для VIP)\n\n"

            "👤 <b>Профиль:</b>\n"
            "/profile - Просмотр профиля\n"
            "/settings - Настройки\n"
            "/language - Изменить язык\n\n"

            "📖 <b>История и избранное:</b>\n"
            "/history - История поиска\n"
            "/favorites - Избранные результаты\n"
            "/export - Экспорт истории\n\n"

            "⭐ <b>VIP:</b>\n"
            "/vip - Информация о VIP\n"
            "/promo - Использовать промокод\n\n"

            "ℹ️ <b>Информация:</b>\n"
            "/help - Эта справка\n"
            "/commands - Полный список команд\n"
            "/faq - Часто задаваемые вопросы\n"
            "/support - Техническая поддержка\n\n"

            "🔔 <b>Уведомления:</b>\n"
            "/notifications - Просмотр уведомлений\n\n"

            "🛠 <b>Системные:</b>\n"
            "/ping - Проверка работы бота\n"
            "/status - Статус бота\n"
        )
    elif language == 'en':
        help_text = (
            "<b>📚 Bot Commands Help</b>\n\n"
            "🔍 <b>Search:</b>\n"
            "/search - Start search\n"
            "/bulk - Bulk search (VIP only)\n\n"

            "👤 <b>Profile:</b>\n"
            "/profile - View profile\n"
            "/settings - Settings\n"
            "/language - Change language\n\n"

            "📖 <b>History and Favorites:</b>\n"
            "/history - Search history\n"
            "/favorites - Favorite results\n"
            "/export - Export history\n\n"

            "⭐ <b>VIP:</b>\n"
            "/vip - VIP information\n"
            "/promo - Use promo code\n\n"

            "ℹ️ <b>Information:</b>\n"
            "/help - This help\n"
            "/commands - Full command list\n"
            "/faq - Frequently asked questions\n"
            "/support - Technical support\n\n"

            "🔔 <b>Notifications:</b>\n"
            "/notifications - View notifications\n\n"

            "🛠 <b>System:</b>\n"
            "/ping - Check bot operation\n"
            "/status - Bot status\n"
        )
    else:  # Default to Turkmen
        help_text = (
            "<b>📚 Bot Komandalary Kömegi</b>\n\n"
            "🔍 <b>Gözleg:</b>\n"
            "/search - Gözlege başlamak\n"
            "/bulk - Köpçülikleýin gözleg (diňe VIP)\n\n"

            "👤 <b>Profil:</b>\n"
            "/profile - Profili görmek\n"
            "/settings - Sazlamalar\n"
            "/language - Dili üýtgetmek\n\n"

            "📖 <b>Taryh we Bellikler:</b>\n"
            "/history - Gözleg taryhy\n"
            "/favorites - Saýlanan netijeler\n"
            "/export - Taryhy eksport etmek\n\n"

            "⭐ <b>VIP:</b>\n"
            "/vip - VIP barada maglumat\n"
            "/promo - Promo kod ulanmak\n\n"

            "ℹ️ <b>Maglumat:</b>\n"
            "/help - Bu kömek\n"
            "/commands - Doly komandalar sanawy\n"
            "/faq - Köp soralýan soraglar\n"
            "/support - Tehniki goldaw\n\n"

            "🔔 <b>Bildirişler:</b>\n"
            "/notifications - Bildirişleri görmek\n\n"

            "🛠 <b>Ulgam:</b>\n"
            "/ping - Botuň işleýşini barlamak\n"
            "/status - Bot statusy\n"
        )

    # Create keyboard
    keyboard = [
        [InlineKeyboardButton(get_message('commands_button', language), callback_data="user_commands")],
        [InlineKeyboardButton(get_message('faq_button', language), callback_data="faq_view")],
        [InlineKeyboardButton(get_message('support_button', language), callback_data="support")],
        [InlineKeyboardButton(get_message('back', language), callback_data="main_menu")]
    ]

    # Send help message
    query.edit_message_text(
        help_text,
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )
