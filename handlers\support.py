# Import telegram modules with try/except to avoid IDE warnings
try:
    from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ParseMode
    from telegram.ext import CallbackContext
except ImportError:
    # Define placeholder classes for type hints
    class Update: pass
    class CallbackContext: pass
    class InlineKeyboardButton: pass
    class InlineKeyboardMarkup: pass
    class ParseMode:
        HTML = "HTML"

from utils.db import Database
from utils.languages import get_message
from config import SUPPORT_CONTACT

def support_command(update: Update, context: CallbackContext):
    """Handle /support command"""
    user_id = update.effective_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Create keyboard with support contact button
    keyboard = [
        [InlineKeyboardButton(get_message('support_contact', language), url=f"https://t.me/{SUPPORT_CONTACT.replace('@', '')}")],
        [InlineKeyboardButton("⬅️ " + get_message('back', language), callback_data="main_menu")]
    ]

    # Send support message
    update.message.reply_text(
        f"<b>{get_message('support_title', language)}</b>\n\n{get_message('support_description', language)}",
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def support_callback(update: Update, context: CallbackContext):
    """Handle support callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Create keyboard with support contact button
    keyboard = [
        [InlineKeyboardButton(get_message('support_contact', language), url=f"https://t.me/{SUPPORT_CONTACT.replace('@', '')}")],
        [InlineKeyboardButton("⬅️ " + get_message('back', language), callback_data="main_menu")]
    ]

    # Edit message with support information
    query.edit_message_text(
        f"<b>{get_message('support_title', language)}</b>\n\n{get_message('support_description', language)}",
        parse_mode=ParseMode.HTML,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )
